import I18N, { getLang } from '@/utils/I18N';
import React, { PureComponent } from 'react';
import { Row, Col, Tooltip } from 'tntd';
import { connect } from 'dva';
import GGEditor, { Flow, RegisterNode, Command, ContextMenu, NodeMenu, EdgeMenu, MultiMenu, CanvasMenu } from 'gg-editor';
import { transformRuleAndIndexFieldList } from '@/utils/decision';
import { decisionTreeLang } from '../../constants/lang';
import FlowToolbar from './FlowToolbar';
import FlowItemPanel from './FlowItemPanel';
import FlowDetailPanel from './FlowDetailPanel';
import './style/style.less';
import './style/menu.less';

GGEditor.setTrackable(false); // 关闭g6的埋点数据

class FlowPage extends PureComponent {
    state = {
        toolTipInfo: {
            visible: false,
            top: 0,
            left: 0
        },
        detailRef: {}
    };

    constructor(props) {
        super(props);
        this.getFlowData = this.getFlowData.bind(this);
        props.bindThis(this);
    }

    componentDidMount() {
        let { decisionTreeStore, isAdd, globalStore } = this.props;
        let { allMap } = globalStore;
        let { dialogData, serviceFieldListObj } = decisionTreeStore;
        let { editorModelData } = dialogData;
        let { flowData, appName, orgCode } = editorModelData;

        this.page.changeAddEdgeModel({
            // 将线设置为圆角折线
            shape: 'flow-smooth'
        });
        // 这里数据结构和之前保持一致
        const ruleAndIndexFieldList = transformRuleAndIndexFieldList(allMap, appName, orgCode);
        if (isAdd) {
            // 后端说这个用户没权限的场景 还是要在inputData中返回到后端
            const indexFieldList = ruleAndIndexFieldList.filter((i) => i.bizType === 'index'); // 指标
            flowData.nodes.forEach((item) => {
                if (!item.decisions && item.fieldDetail && item.fieldType === 'INDEX_FIELD') {
                    // 节点，不是决策节点
                    const findObj = indexFieldList.find((k) => k.name === item.nodeName);
                    if (!findObj) {
                        item.label = item.fieldDetail?.name || I18N.flow.index.jieDian;
                        item.allLabel = item.fieldDetail?.name || I18N.flow.index.jieDian;
                    }
                }
            });
            // const indexFieldList = ruleAndIndexFieldList.filter((i) => i.bizType === 'index'); // 指标
            // flowData.nodes.forEach((item) => {
            //     if (!item.decisions && item.fieldDetail && item.fieldType === 'INDEX_FIELD') {
            //         // 节点，不是决策节点
            //         const findObj = indexFieldList.find((k) => k.name === item.nodeName);
            //         if (!findObj) {
            //             item.label = I18N.flow.index.jieDian;
            //             item.fieldDetail = null;
            //             item.nodeName = '';
            //         }
            //     }
            // });
        } else {
            const fieldListAll = ruleAndIndexFieldList;
            flowData?.nodes?.forEach((item) => {
                if (item.shape === 'flow-capsule' && item.fieldDetail && item.fieldDetail.dataType === 'enum') {
                    const fieldDetail = item?.fieldDetail;
                    const findobj = fieldListAll?.find((item) => item.name === fieldDetail?.name);
                    if (findobj) {
                        item.fieldDetail.fieldInfo = findobj?.fieldInfo;
                    }
                }
                if (item.shape === 'flow-capsule' && item.fieldDetail) {
                    const fieldDetail = item?.fieldDetail;
                    const findObj = fieldListAll?.find((k) => k.name === fieldDetail?.name);
                    if (!findObj) {
                        item.label = item.fieldDetail?.name || I18N.flow.index.jieDian;
                        item.allLabel = item.fieldDetail?.name || I18N.flow.index.jieDian;
                        // item.label = I18N.flow.index.jieDian;
                        // item.fieldDetail = null;
                    } else {
                        item.label = findObj.dName?.length > 10 ? findObj?.dName?.slice(0, 10) + '...' : findObj.dName;
                        item.allLabel = findObj.dName;
                    }
                }
            });
        }
        this.page.read(flowData); // 初始化数据
    }

    componentWillUnmount() {
        // 清理定时器，防止内存泄漏
        if (this.undoTimeout) {
            clearTimeout(this.undoTimeout);
            this.undoTimeout = null;
        }
    }

    renderFlow() {
        // let grid = {
        // 	cell: 20,
        // 	line: {
        // 		fill: 'red',
        // 		stroke: '#dcdcdc',
        // 		shape: 'square'
        // 	}
        // };
        let align = {
            item: true,
            grid: true
        };
        return (
            <Flow
                className="flow"
                ref={(g) => (this.page = g ? g.page : null)}
                align={align}
                item={true}
                noEndEdge={false} // 是否支持悬空边
                onAnchorDrop={(e) => {
                    if (e.type !== 'node') {
                        const beforeNodeId = e?.currentShape?.getItem()?.id;
                        const targetNodeId = e?.shape?.getItem()?.id;

                        if (beforeNodeId === targetNodeId) {
                            return null;
                        }
                        // e?.currentShape?.eventPreFix?.includes('edge') &&
                        //     this.state.detailRef.update(e?.currentShape?.getItem(), { label: '', conditionsGroup: { conditions: [], allLabel: "" } });
                    }
                }}
                onNodeMouseEnter={(e) => {
                    if (e?.item?.model?.allLabel?.length > 10) {
                        const { top, left } = document.querySelector('.flow').getBoundingClientRect() || {};
                        const { x, y } = e.item.graph.getDomPoint({
                            x: e.item.model.x,
                            y: e.item.model.y
                        });
                        this.setState({
                            toolTipInfo: {
                                visible: true,
                                top: y + top - e.item.bbox.height / 2,
                                left: x + left,
                                text: e.item.model.allLabel
                            }
                        });
                    }
                }}
                onNodeMouseLeave={(e) => {
                    if (e?.item?.model?.allLabel?.length > 10) {
                        this.setState({
                            toolTipInfo: {
                                visible: false,
                                top: 0,
                                left: 0,
                                text: null
                            }
                        });
                    }
                }}
                onEdgeMouseEnter={(e) => {
                    if (e?.item?.model?.allLabel?.length > 10) {
                        const { top, left } = document.querySelector('.flow').getBoundingClientRect() || {};
                        const { x, y } = e.item.graph.getDomPoint({
                            x: e.shape.getBBox?.()?.x || e?.x,
                            y: e.shape.getBBox?.()?.y || e?.y
                        });
                        this.setState({
                            toolTipInfo: {
                                visible: true,
                                top: y + top, // + e.shape.getBBox().height / 2,
                                left: x + left + e.shape.getBBox().width / 2,
                                text: e.item.model.allLabel
                            }
                        });
                    }
                }}
                onEdgeMouseLeave={(e) => {
                    if (this.state.toolTipInfo?.visible) {
                        this.setState({
                            toolTipInfo: {
                                visible: false,
                                top: 0,
                                left: 0,
                                text: null
                            }
                        });
                    }
                }}
            />
        );
    }

    getFlowData() {
        let flowData = this.page.getItems();
        return flowData;
    }

    render() {
        const { toolTipInfo } = this.state;
        const { isAdd, decisionTreeStore } = this.props;
        let { isEditor } = decisionTreeStore;
        return (
            <>
                <GGEditor
                    onBeforeCommandExecute={({ command }) => {
                        // 只读模式下不支持删除／新增／更新
                        if (!isEditor && !isAdd) {
                            if (['add', 'delete', 'update'].includes(command.name)) {
                                // 阻止命令执行，避免无限循环
                                return false;
                            }
                        }
                    }}
                    className="flow-editor"
                    ref={(g) => (this.editor = g ? g.editor : null)}>
                    <Row type="flex" className="flow-editor-bd">
                        <Col span={3} className="flow-editor-bar">
                            {!(!isEditor && !isAdd) && <FlowItemPanel />}
                        </Col>
                        <Col span={14} className="flow-editor-content">
                            <Row type="flex" className="flow-editor-hd">
                                <Col span={24}>
                                    <FlowToolbar />
                                </Col>
                            </Row>
                            {this.renderFlow()}
                        </Col>
                        <Col span={7} className="flow-editor-bar">
                            <FlowDetailPanel
                                onRef={(ref) => {
                                    this.setState({
                                        detailRef: { update: ref }
                                    });
                                }}
                            />
                            {/* <FlowMinimap/>*/}
                        </Col>
                    </Row>
                    <RegisterNode
                        name="flow-policy"
                        extend="flow-capsule"
                        config={{
                            anchor: [[0.5, 0]]
                        }}
                    />
                    {/* 节点右键菜单 */}
                    <ContextMenu className="flow-editor-menu-wrap">
                        <NodeMenu>
                            {/* lang: 复制*/}
                            <Command name="copy">{decisionTreeLang.twoStep('copy')}</Command>
                            {/* lang: 删除*/}
                            <Command name="delete">{decisionTreeLang.twoStep('delete')}</Command>
                            <hr />
                            {/* lang: 向上一层*/}
                            <Command name="toFront">{decisionTreeLang.twoStep('toFront')}</Command>
                            {/* lang: 向下一层*/}
                            <Command name="toBack">{decisionTreeLang.twoStep('toBack')}</Command>
                        </NodeMenu>
                        <EdgeMenu>
                            <Command name="delete">{decisionTreeLang.twoStep('delete')}</Command>
                        </EdgeMenu>
                        <MultiMenu>
                            <Command name="copy">{decisionTreeLang.twoStep('copy')}</Command>
                            <Command name="delete">{decisionTreeLang.twoStep('delete')}</Command>
                        </MultiMenu>
                        <CanvasMenu>
                            {/* lang: 全选*/}
                            <Command name="selectAll">{decisionTreeLang.twoStep('selectAll')}</Command>
                            {/* lang: 粘贴*/}
                            <Command name="paste">{decisionTreeLang.twoStep('paste')}</Command>
                            <hr />
                            {/* lang: 撤销*/}
                            <Command name="undo">{decisionTreeLang.twoStep('cancel')}</Command>
                            {/* lang: 重做*/}
                            <Command name="redo">{decisionTreeLang.twoStep('redo')}</Command>
                            <hr />
                            {/* lang: 实际尺寸*/}
                            <Command name="resetZoom">{decisionTreeLang.twoStep('actualSize')}</Command>
                            {/* lang: 自适应尺寸*/}
                            <Command name="autoZoom">{decisionTreeLang.twoStep('adaptiveSize')}</Command>
                        </CanvasMenu>
                    </ContextMenu>
                </GGEditor>
                {toolTipInfo.visible && (
                    <Tooltip title={toolTipInfo.text} visible={true}>
                        <div
                            style={{
                                position: 'fixed',
                                top: toolTipInfo.top,
                                left: toolTipInfo.left
                            }}
                        />
                    </Tooltip>
                )}
            </>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global,
    decisionTreeStore: state.decisionTree
}))(FlowPage);
