import request, { downloadReport } from '../utils/request';
import { getHeader, getUrl, deleteEmptyObjItem } from './common';

/* 共用：获取回测任务列表 */

// 获取回测列表
const getReplayTaskList = async (params) => {
    params = deleteEmptyObjItem(params);
    return request(getUrl('/lab/replay/list', params), {
        method: 'GET',
        headers: getHeader()
    });
};

// 获取回测详情列表
const getReplayTaskDetailList = async (params) => {
    return request(getUrl('/lab/replay/detail/list', params), {
        method: 'GET',
        headers: getHeader()
    });
};

// 回测详情-报文
const getReplayTaskDetailMsg = async (params) => {
    return request(getUrl('/lab/replay/event/message', params), {
        method: 'GET',
        headers: getHeader()
    });
};

// 新增任务——获取回测任务运行数量和事件
const getReplayTaskRunInfo = async (params) => {
    return request('/lab/replay/estimate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            ...getHeader()
        },
        body: params
    });
};

// 新增任务——确定
const createTask = async (params) => {
    return request('/lab/replay/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            ...getHeader()
        },
        body: params
    });
};

// 重启任务
const reStartTask = async (params) => {
    return request('/lab/replay/restart', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            ...getHeader()
        },
        body: params
    });
};

// 终止任务
const terminateTask = async (params) => {
    return request('/lab/replay/terminate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            ...getHeader()
        },
        body: params
    });
};

// 字段指标下拉列表
const getFieldsList = async (params) => {
    return request('/lab/replay/condition', {
        method: 'GET',
        headers: getHeader()
    });
};

/* 以下为规则回测接口 */

// 规则回测报告
const getRuleSetReport = async (params) => {
    return request(getUrl('/lab/replay/ruleset/event/report', params), {
        method: 'GET',
        headers: getHeader()
    });
};

// 规则集下拉列表
const getRuleSetSelect = async (params) => {
    return request(getUrl('/rulesetVersion/select', params), {
        method: 'GET',
        headers: getHeader()
    });
};

const getReplayTaskDetail = async (params) => {
    params = deleteEmptyObjItem(params);
    return request(getUrl('/lab/admin/policySet/replay/hitDetail', params), {
        method: 'GET',
        headers: getHeader()
    });
};

// 概览
const getRuleSetOverview = async (params) => {
    return request(getUrl('/lab/replay/ruleset/result', params), {
        method: 'GET',
        headers: getHeader()
    });
};

// 概览-导出
const ruleSetOverviewExport = async (params, name) => {
    return downloadReport(
        getUrl('/lab/replay/ruleset/result/export', params),
        {
            method: 'GET',
            headers: getHeader()
        },
        name,
        'xls'
    );
};

/* 以下为策略回测接口 */

// 新增回测——策略下拉框数据
const getPolicyOptions = async (params) => {
    return request(getUrl('/lab/replay/policy/select', params), {
        method: 'GET',
        headers: getHeader()
    });
};

// 新增回测——策略版本下拉框数据
const getPolicyVersionOptions = async (params) => {
    return request(getUrl('/lab/replay/policy/version/select', params), {
        method: 'GET',
        headers: getHeader()
    });
};

// 概览
const getPolicyOverview = async (params) => {
    return request(getUrl('/lab/replay/policy/overview', params), {
        method: 'GET',
        headers: getHeader()
    });
};

// 概览-规则命中排行
const getPolicyRuleHitRanking = async (params) => {
    return request(getUrl('/lab/replay/policy/overview/rulehitranking', params), {
        method: 'GET',
        headers: getHeader()
    });
};

// 概览-导出
const policyOverviewExport = async (params, name) => {
    return downloadReport(
        getUrl('/lab/replay/policy/overview/export', params),
        {
            method: 'GET',
            headers: getHeader()
        },
        name,
        'xls'
    );
};

// 策略回测报告
const getPolicyReport = async (params) => {
    return request(getUrl('/replay/policy/report', params), {
        method: 'GET',
        headers: getHeader()
    });
};

// 策略三方列表查询
const getPolicyThirdservice = async (params) => {
    return request(getUrl('/lab/replay/policy/thirdservice', params), {
        method: 'GET',
        headers: getHeader()
    });
};

// 获取过滤条件下拉框数据
const getCondition = async (params) => {
    return request(getUrl('/lab/replay/condition', params), {
        method: 'GET',
        headers: getHeader()
    });
};

// 获取某个进件ID下的所有报告
const getAllReportByEntryId = async (param, type) => {
    return request(getUrl(`/report/entry/detail/${type}`, param), {
        method: 'GET'
    });
};

// 批量决策任务列表
const getSelectList = (params) => {
    return request(getUrl('/batchDecision/history/getSelectList', params), {
        method: 'GET'
    });
};

// 获取运行区规则集下拉列表
const getRunRuleSetSelect = async (params) => {
    return request(getUrl('/rulesetVersion/select', params), {
        method: 'GET',
        headers: getHeader()
    });
};

// 获取编辑区规则集下拉列表
const getEditRuleSetSelect = async (params) => {
    return request(getUrl('/ruleset/select', params), {
        method: 'GET',
        headers: getHeader()
    });
};

// 获取策略下拉列表
const getSelect = async () => {
    return request('/policy/dict', {
        method: 'GET'
    });
};

export default {
    getReplayTaskList,
    getReplayTaskDetailList,
    getReplayTaskDetailMsg,
    createTask,
    reStartTask,
    terminateTask,
    getRuleSetReport,
    getReplayTaskDetail,
    getRuleSetOverview,
    ruleSetOverviewExport,
    getReplayTaskRunInfo,
    getPolicyOptions,
    getPolicyVersionOptions,
    getPolicyOverview,
    getPolicyRuleHitRanking,
    policyOverviewExport,
    getPolicyReport,
    getPolicyThirdservice,
    getCondition,
    getRuleSetSelect,
    getAllReportByEntryId,
    getFieldsList,
    getSelectList,
    getRunRuleSetSelect,
    getEditRuleSetSelect,
    getSelect
};
