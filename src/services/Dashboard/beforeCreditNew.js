import request, { downloadFileHandle, downloadReport } from '@/utils/request';
import { getUrl } from '../common';

const getPolicyList = async (param) => {
    return request(getUrl('/dashboard/policy/select', param), {
        method: 'GET'
    });
};

const getConfig = async (param) => {
    return request(getUrl('/dashboard/preloan/config', param), {
        method: 'GET'
    });
};

const getData = async (param) => {
    return request(getUrl('/dashboard/preloan/getEntryInfoStatsData', param), {
        method: 'GET'
    });
};
const getChart = async (param) => {
    return request(getUrl(`/dashboard/${param?.type}`, param), {
        method: 'GET'
    });
};
const ExportChart = async (param, name, code, fileType) => {
    return downloadFileHandle(
        getUrl(`/dashboard/${code}/export`, param),
        {
            method: 'GET'
        },
        name,
        fileType
    );
};

const getBarChart = async (param) => {
    return request(getUrl('/dashboard/preloan/getBarChart', param), {
        method: 'GET'
    });
};

const getLineChart = async (param) => {
    return request(getUrl('/dashboard/preloan/getLineChart', param), {
        method: 'GET'
    });
};
const getPieChart = async (param) => {
    return request(getUrl('/dashboard/preloan/getPieChart', param), {
        method: 'GET'
    });
};
const getTableData = async (param) => {
    return request(getUrl('/dashboard/preloan/getTableData', param), {
        method: 'GET'
    });
};

const ExportData = async (param, name, fileType) => {
    return downloadFileHandle(
        getUrl('/dashboard/preloan/getEntryInfoStatsData/export', param),
        {
            method: 'GET'
        },
        name,
        fileType
    );
};

const ExportBarChart = async (param, name, fileType) => {
    return downloadFileHandle(
        getUrl('/dashboard/preloan/getBarChart/export', param),
        {
            method: 'GET'
        },
        name,
        fileType
    );
};

const ExportLineChart = async (param, name, fileType) => {
    return downloadFileHandle(
        getUrl('/dashboard/preloan/getLineChart/export', param),
        {
            method: 'GET'
        },
        name,
        fileType
    );
};
const ExportPieChart = async (param, name, fileType) => {
    return downloadFileHandle(
        getUrl('/dashboard/preloan/getPieChart/export', param),
        {
            method: 'GET'
        },
        name,
        fileType
    );
};
const ExportTableData = async (param, name, fileType) => {
    return downloadFileHandle(
        getUrl('/dashboard/preloan/getTableData/export', param),
        {
            method: 'GET'
        },
        name,
        fileType
    );
};

const getPolicyDetail = (params) => {
    return request(getUrl('/dashboard/policy/select', params), {
        method: 'GET'
    });
};
// 获取策略版本简略列表
const getPolicyVersionList = async (params) => {
    return request(getUrl('/dashboard/policy/version', params), {
        method: 'GET'
    });
};

// 获取全部策略列表
const getAllPolicyList = async (params) => {
    return request(getUrl('/policy/list', params), {
        method: 'GET'
    });
};

// 批量决策任务列表
const getSelectList = (params) => {
    return request(getUrl('/batchDecision/history/getSelectList', params), {
        method: 'GET'
    });
};

export default {
    getPolicyList,
    getConfig,
    getData,
    getBarChart,
    getLineChart,
    getPieChart,
    getTableData,
    ExportBarChart,
    ExportLineChart,
    ExportPieChart,
    ExportTableData,
    ExportData,
    getChart,
    ExportChart,
    getPolicyDetail,
    getPolicyVersionList,
    getSelectList,
    getAllPolicyList
};
