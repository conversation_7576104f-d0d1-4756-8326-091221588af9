import { getHeader, getUrl, deleteEmptyObjItem } from '../common';
import request, { PostForm, downloadFileHandle } from '../../utils/request';

// 获取决策表数据
const getPageList = async (params) => {
    return request(getUrl('/decisiontool/page/' + params.type, params), {
        method: 'GET',
        headers: getHeader()
    });
};
// 决策工具下线
const offlineHandle = async (params) => {
    return request('/decisiontool/offLine', {
        method: 'POST',
        body: params,
        headers: {
            ...getHeader(),
            'Content-Type': 'application/json'
        }
    });
};
const getDecisionToolCalculateResult = async (params) => {
    return request('/decisiontool/test', {
        method: 'POST',
        body: { ...params }
    });
};

// 查询节点
const getNodes = async (params) => {
    return request(getUrl('/decisiontool/nodeMessage', params), {
        method: 'GET'
    });
};
// 缺失项校验
const missItemCheck = async (params) => {
    return request(getUrl('/decisiontool/checkField', params), {
        method: 'GET'
    });
};

// 矩阵复制
const cloneDecisionTool = async (params) => {
    return request('/decisiontool/copy', {
        method: 'POST',
        body: params,
        headers: {
            ...getHeader(),
            'Content-Type': 'application/json'
        }
    });
};

//决策工具查看版本
const historyList = async (params) => {
    return request(getUrl('/decisiontool/historyList', deleteEmptyObjItem(params)), {
        method: 'GET'
    });
};

// 新增决策工具
const addDecisionTool = async (params) => {
    return request('/decisiontool/create', {
        method: 'POST',
        body: params,
        headers: {
            ...getHeader(),
            'Content-Type': 'application/json'
        }
    });
};

// 修改决策工具
const modifyDecisionTool = async (params) => {
    return request('/decisiontool/update', {
        method: 'POST',
        body: params,
        headers: {
            ...getHeader(),
            'Content-Type': 'application/json'
        }
    });
};

// 版本覆盖
const decisiontoolCover = async (params) => {
    return request('/decisiontool/cover', {
        method: 'POST',
        body: params,
        headers: {
            ...getHeader(),
            'Content-Type': 'application/json'
        }
    });
};

// 校验名称不能重复
const checkCode = async (params) => {
    return request('/decisiontool/checkCode', {
        method: 'POST',
        body: { ...params }
    });
};

// 点击决策树预测
const decisiontoolTest = async (params) => {
    return request('/decisiontool/test', {
        method: 'POST',
        body: params,
        headers: {
            ...getHeader(),
            'Content-Type': 'application/json'
        }
    });
};

// 决策工具批量上线
const batchOnLine = async (params) => {
    return request('/decisiontool/batchOnLine', {
        method: 'POST',
        body: params,
        headers: {
            ...getHeader(),
            'Content-Type': 'application/json'
        }
    });
};
//复制
const copyModel = async (params) => {
    return request('/decisiontool/copy', {
        method: 'POST',
        body: { ...params },
        headers: {
            ...getHeader(),
            'Content-Type': 'application/json'
        }
    });
};
// 授权
const decisionAuthorize = (params) => {
    return request('/decisiontool/authorize', {
        method: 'POST',
        headers: {
            ...getHeader(),
            'Content-Type': 'application/json'
        },
        body: { ...params }
    });
};

// 删除
const decisiontoolDelete = (params) => {
    return request('/decisiontool/delete', {
        method: 'POST',
        headers: {
            ...getHeader(),
            'Content-Type': 'application/json'
        },
        body: { ...params }
    });
};

// 校验名称是否相同
const checkName = (params) => {
    return request('/decisiontool/checkName', {
        method: 'POST',
        headers: {
            ...getHeader(),
            'Content-Type': 'application/json'
        },
        body: { ...params }
    });
};

// 获取处置方式
const getDealTypes = async (params) => {
    return request(getUrl('/decisiontool/dealTypeMessage', params), {
        method: 'GET'
    });
};

// 获取处置方式
const getDetail = async (params) => {
    return request(getUrl('/decisiontool/getDetail/' + params.areaType, params), {
        method: 'GET'
    });
};

export default {
    getDetail,
    getDealTypes,
    checkName,
    decisiontoolDelete,
    decisionAuthorize,
    copyModel,
    batchOnLine,
    decisiontoolTest,
    decisiontoolCover,
    addDecisionTool,
    modifyDecisionTool,
    historyList,
    getPageList,
    offlineHandle,
    missItemCheck,
    getNodes,
    getDecisionToolCalculateResult,
    cloneDecisionTool,
    checkCode
};
