import request from '@/utils/request';
import { getUrl, deleteEmptyObjItem, getHeader } from '../common';

const getRelationResult = async (params) => {
    params = deleteEmptyObjItem(params);
    let url = '/relationReference/relationAnalysis';
    if (params.relationType) {
        url += `/${params.relationType}`;
        delete params.relationType;
    }
    return request(getUrl(url, params), {
        method: 'GET',
        headers: getHeader()
    });
};

const checkComponentReference = async (params) => {
    params = deleteEmptyObjItem(params);
    return request(getUrl('/relationReference/checkComponentReference', params), {
        method: 'GET',
        headers: getHeader()
    });
};

const onlineValidate = async (params) => {
    params = deleteEmptyObjItem(params);
    return request(getUrl('/component/onlineValidate', params), {
        method: 'GET',
        headers: getHeader()
    });
};

const batchCheckComponentReference = async (params) => {
    return request('/relationReference/batchCheckComponentReference', {
        method: 'POST',
        headers: {
            ...getHeader(),
            'Content-Type': 'application/json'
        },
        body: params
    });
};

export default {
    batchCheckComponentReference,
    getRelationResult,
    checkComponentReference,
    onlineValidate
};
