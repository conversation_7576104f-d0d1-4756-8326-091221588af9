body,
html {
    width: 100%;
    height: 100%;
}

body {
    height: 100%;
    overflow-y: hidden;
    background-color: #f0f2f5;
    text-rendering: optimizeLegibility;
    text-rendering: initial;
    -webkit-font-smoothing: initial;
    font-family: "Chinese Quote", -apple-system, BlinkMacSystemFont, "Segoe UI",
        "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue",
        Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
        "Segoe UI Symbol";
}

.@{langEnPrefix} {
    .ant-table-thead > tr > th,
    .ant-table-tbody > tr > td {
       word-break: normal;
    }
}

#root {
    height: 100vh;
    position: relative;
    // layout 高度
    .isInIframe {
        .page-global-body {
            padding: 0;
            height: 100vh !important;
        }
    }

    .ant-layout-content {
        min-width: 1024px;
    }

    section[class*="tnt-"] .page-global-body {
        height: ~"calc(100vh - 90px)";
    }

    // 面包屑
    .page-global-header {
        height: 40px;
        line-height: 40px;
        border: none;
        .left-info h2 {
            font-size: 14px;
        }
        .c-breadcrumb {
            height: 40px;
            line-height: 40px;
            > span {
                font-size: 14px;
            }
        }
    }
    .query-list-tab-wrap {
        height: calc(100vh - 90px);
        overflow: auto;
    }
}

.globalSpin {
    width: 100%;
    margin: 40px 0 !important;
    text-align: center;
    display: inherit;
}

.ant-select-disabled,
.ant-input-disabled {
    color: rgba(0, 0, 0, 0.45);
}

.CodeMirror-cursor {
    border-left: thin solid #ffcc00 !important;
}
.must-input::before {
    content: "*";
    color: red;
    margin-right: 5px;
}
.u-required {
    display: inline-block;
    color: #f00;
    vertical-align: middle;
    margin-right: 2px;
}
.metric-name {
    position: relative;
    display:inline-block;
    max-width: 100%;
    vertical-align: middle;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    .arrow-tip {
        position: absolute;
        right: 0;
        top: 0;
    }
}
.m-nodata{
    text-align: center;
    img{
        width: 300px;
    }
    p{
        font-size: 14px;
        color: #666;
    }
}

.tnt-layout.isEmptyLayout .ant-layout-content{
    height: calc(100vh) !important;
}

.tnt-ellipsis{
    line-height: 20px;
}
.tntd-ellipsis{
    line-height: 1.5;
}

@font-face {
    font-family:'DIN-Bold';
    font-display: swap;
    src:url('../sources/fonts/DIN-Bold.otf');
}
@font-face {
    font-family:'PangMenZhengDaoBiaoTiTi';
    font-display: swap;
    src:url('../sources/fonts/PangMenZhengDaoBiaoTiTi-1.ttf');
}
.reference {
    display: inline-block;
    height: 20px;
    width: 20px;
    background-image: url('../sources/images/common/quote.svg');
    background-position: center;
    background-repeat: no-repeat;
}
.ant-cascader-picker-disabled {
    background: rgba(225, 230, 238, 0.71) !important;
    cursor: not-allowed;

    .ant-cascader-picker-label{
        color: rgba(23, 35, 61, 0.3);
    }
}

.ant-empty-image{
    &>svg{
        width: 100% !important;
    }
}
