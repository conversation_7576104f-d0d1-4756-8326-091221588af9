@import "../../base/variables";

:global {
    .policy-detail-header {
        & {
            // margin: 20px 0 30px;
            // width: 1440px;
            height: 40px;
            background: #ffffff;
            box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
        }
        .policy-detail-guide {
            & {
                position: absolute;
                left: 16px; //0;
                line-height: 40px;
            }
            .go-back {
                // width: 60px;
                height: 40px; //50px;
                line-height: 40px; //43px;
                text-align: center;
                float: left;
                margin-right: 5px;
                cursor: pointer;
                i {
                    font-size: 16px; //18px;
                    line-height: 40px; //32px;
                }
                span {
                    position: relative;
                    // top: -1px;
                }
            }
            h3 {
                & {
                    float: left;
                    margin-bottom: 0;
                    color: #333;
                    cursor: pointer;
                    font-size: 14px; //16px;
                    font-weight: normal;
                    max-width: 280px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
                i {
                    position: relative;
                    top: -2px;
                }
            }
        }
    }

    .policy-detail-box .tab-menu {
        & {
            margin: 0 auto 20px;
            // width: 222px;
            padding: 0;
            text-align: center;
            // border: 1px solid #126bfb; //#3498db;
            overflow: hidden;
            // border-radius: 4px;
        }
        span {
            margin: 0;
            padding: 0;
            display: inline-block;
            min-width: 110px;
            height: 30px;
            line-height: 30px;
            font-size: 14px;
            color: #126bfb; //#428bca;
            cursor: pointer;
            width: auto;
            padding: 0 16px;
            border: 1px solid #126bfb;
            transition: all 0.5s cubic-bezier(0, 1, 0.5, 1);
            -webkit-transition: all 0.5s cubic-bezier(0, 1, 0.5, 1);
            -moz-transition: all 0.5s cubic-bezier(0, 1, 0.5, 1);
            &.first{
                border-top-left-radius: 2px;
                border-bottom-left-radius: 2px;
            }
            &.second{
                border-top-right-radius: 2px;
                border-bottom-right-radius: 2px;
            }
        }
        span.active {
            background-color: #126bfb; //#3498db;
            color: #fff !important;
        }
    }
}
