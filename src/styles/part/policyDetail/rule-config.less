@import "../../base/variables";
@import "./Less/rule-config-en.less";
:global {
    .rule-config-wrap {
        & {
        }

        .basic-info-title {
            text-align: right;
            line-height: 28px; //32px;
            margin-right: 13px;
        }

        .basic-info-text {
            text-align: center;
            line-height: 28px; //32px;
        }

        .add-new-operation,
        .add-new-filter {
            span {
                &:hover {
                    color: @blue;
                    cursor: pointer;
                }

                i {
                    margin-right: 8px;
                }
            }

            a.disabled,
            a.disabled:hover {
                // color: #999;
                color: @disabled-color;
                cursor: not-allowed;
                text-decoration: none;
            }
        }

        .add-new-filter {
            line-height: 32px;
        }

        .ant-radio-group {
            line-height: 32px;
        }

        .w365 {
            width: 365px;
        }

        .w50 {
            width: 50px;
        }

        .w200 {
            width: 200px;
        }

        .ml-1 {
            margin-left: -1px;
        }

        .rule-detail {
            & {
                position: relative;
                margin: 0 0 16px;
                padding: 20px 0;
                //border: 1px solid #D4D4D4;
                // border-bottom: 1px solid @grey-d; //#d4d4d4;
            }

            &:last-child {
                margin-bottom: 0;
            }

            .rule-detail-title {
                & {
                    position: relative;
                    margin: 0;
                    top: -12px;
                    font-size: 14px;
                    font-weight: normal;
                    line-height: 20px;
                    padding-left: 12px;
                }

                &:before {
                    position: absolute;
                    width: 4px;
                    height: 16px;
                    background: #126bfb;
                    top: 1px;
                    left: 0;
                    content: "";
                }
                &::after {
                    position: absolute;
                    width: calc(100% - 135px);
                    height: 1px;
                    background: #b2bcd1;
                    top: 10px;
                    margin-left: 20px;
                    content: "";
                }
            }
            .configEnglish::after {
                width: calc(100% - 290px);
            }
            .config::after {
                width: calc(100% - 195px);
            }

            .rule-detail-title.title1::after {
                position: absolute;
                width: calc(100% - 88px);
                height: 1px;
                background: #b2bcd1;
                top: 10px;
                left: 88px;
                content: "";
            }

            .rule-detail-content {
                & {
                    margin-top: 5px;
                    margin-bottom: 0;
                }

                &.btns {
                    button {
                        margin-right: 12px;
                    }
                }

                .rule-operation-item {
                    & {
                        margin-top: 10px;
                    }
                }

                .ant-checkbox-group {
                    line-height: 32px;
                }
            }
        }

        .rule-btns {
            & {
                margin-left: 20.83333%;
                padding-left: 24px;
            }

            button {
                margin-right: 12px;
            }
        }

        .inline-span {
            display: inline-block;
            vertical-align: top;
            line-height: 32px;
        }
    }

    .basic-info-oper {
        & {
            font-size: 16px;
            vertical-align: top;
            // color: #999;
            margin-left: 10px;
            display: inline-block;
            line-height: 28px !important;
            display: flex;
            align-items: center;
        }

        i {
            margin-right: 10px;
            cursor: pointer;
        }

        i:last-child {
            margin-right: 0;
        }

        i.add:hover {
            color: @blue;
        }

        i.delete:hover {
            color: #f00;
        }

        i.show-more {
            transform: rotate(-90deg);
            -moz-transform: rotate(-90deg);
            -webkit-transform: rotate(-90deg);
        }

        i.show-more.hide {
            transform: rotate(90deg);
            -moz-transform: rotate(90deg);
            -webkit-transform: rotate(90deg);
        }
    }

    .param-tip-icon {
        font-size: 16px;
        color:@text-color; //#999;
        cursor: pointer;
        line-height: 28px;
    }
}
