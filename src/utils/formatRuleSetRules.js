import { uniqBy } from 'lodash';
import { isJSON } from './isJSON';

export function formatRuleSetRules(ruleSetRules) {
    ruleSetRules &&
        ruleSetRules.map((item, index) => {
            ruleSetRules[index]['ruleActionDTO'] = item['ruleActionDTO'] && item['ruleActionDTO'] ? item['ruleActionDTO'] : [];
            ruleSetRules[index]['ruleActionDTO'] = item['ruleActionDTO'].map((v) => {
                v['action'] = v['action'] && isJSON(v['action']) ? JSON.parse(v['action']) : [];
                return v;
            });
            ruleSetRules[index]['triggers'] = item['triggers'] && isJSON(item['triggers']) ? JSON.parse(item['triggers']) : [];
            if (item['ruleConditionDTO']) {
                ruleSetRules[index]['ruleConditionDTO']['params'] =
                    item['ruleConditionDTO']['params'] && isJSON(item['ruleConditionDTO']['params'])
                        ? JSON.parse(item['ruleConditionDTO']['params'])
                        : [];

                ruleSetRules[index]['ruleConditionDTO']['children'] &&
                    ruleSetRules[index]['ruleConditionDTO']['children'].map((conditionItem, conditionIndex) => {
                        if (conditionItem.type === 'alias') {
                            let aliasItem = ruleSetRules[index]['ruleConditionDTO']['children'][conditionIndex];
                            let params = [];
                            if (conditionItem['params'] && isJSON(conditionItem['params'])) {
                                params = JSON.parse(conditionItem['params']);
                            }
                            aliasItem['params'] = uniqBy(params, 'name');
                        }
                    });
            }

            // 如果是if规则，有子集
            item.children &&
                item.children.map((subItem, subIndex) => {
                    ruleSetRules[index]['children'][subIndex]['ruleActionDTO'] =
                        subItem['ruleActionDTO'] && subItem['ruleActionDTO'] ? subItem['ruleActionDTO'] : [];

                    ruleSetRules[index]['children'][subIndex]['ruleActionDTO'] = subItem['ruleActionDTO'].map((v) => {
                        v['action'] = v['action'] && isJSON(v['action']) ? JSON.parse(v['action']) : [];
                        return v;
                    });
                    ruleSetRules[index]['children'][subIndex]['triggers'] =
                        subItem['triggers'] && isJSON(subItem['triggers']) ? JSON.parse(subItem['triggers']) : [];
                    subItem['ruleConditionDTO'] &&
                        subItem['ruleConditionDTO']['children'] &&
                        subItem['ruleConditionDTO']['children'].map((conditionItem, conditionIndex) => {
                            if (conditionItem.type === 'alias') {
                                let aliasItem2 = subItem['ruleConditionDTO']['children'][conditionIndex];

                                let params = [];
                                if (conditionItem['params'] && isJSON(conditionItem['params'])) {
                                    params = JSON.parse(conditionItem['params']);
                                }
                                aliasItem2['params'] = uniqBy(params, 'name');
                            }
                        });
                });
        });
    return ruleSetRules;
}
