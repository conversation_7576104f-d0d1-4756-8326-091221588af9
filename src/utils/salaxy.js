import history from '@/utils/history';

export function getSimpleCfgList(cfg<PERSON><PERSON>, currentIndexObj) {
    // console.log("=======getSimpleCfgList======>>>>>>>>>>>>");
    let list = [];
    let attachFieldsObj = currentIndexObj && currentIndexObj.attachFieldsObj ? currentIndexObj.attachFieldsObj : null;
    cfgJson &&
        cfgJson['params'] &&
        cfgJson['params'].map((item) => {
            item.children &&
                item.children.map((subItem) => {
                    let value;
                    if (attachFieldsObj && subItem.name && subItem.name.indexOf('attachFields.') > -1) {
                        let attName = subItem['name'].split('attachFields.')[1];
                        value = attachFieldsObj[attName] ? attachFieldsObj[attName] : undefined;
                    } else {
                        value = currentIndexObj && currentIndexObj[subItem.name] ? currentIndexObj[subItem.name] : undefined;
                    }
                    let obj = {
                        name: subItem.name,
                        componentType: subItem.componentType,
                        type: subItem.type,
                        value,
                        mapType: subItem.mapType ? subItem.mapType : null,
                        selectName: subItem.selectName ? subItem.selectName : null,
                        selectType: subItem.selectType ? subItem.selectType : null,
                        selectOption: subItem.selectOption ? subItem.selectOption : null,
                        willChangeOther: subItem['willChangeOther'] ? subItem['willChangeOther'] : [],
                        willChangeSelf: subItem['willChangeSelf'] ? subItem['willChangeSelf'] : null,
                        willChangeMoreSelf: subItem['willChangeMoreSelf'] ? subItem['willChangeMoreSelf'] : [],
                        willChangeParent: subItem['willChangeParent'] ? subItem['willChangeParent'] : []
                    };
                    list.push(obj);
                });
        });
    return list;
}

export function getRuleCfgList(cfgJson, params) {
    let list = [];
    cfgJson &&
        cfgJson['params'] &&
        cfgJson['params'].map((item, index) => {
            item.children &&
                item.children.map((subItem, subIndex) => {
                    let param = params.find((pItem) => pItem.name === subItem.name);
                    let obj = {
                        name: subItem.name,
                        componentType: subItem.componentType,
                        type: subItem.type ? subItem.type : 'string',
                        value: param && param.value ? param.value : subItem.defaultValue,
                        mapType: subItem.mapType ? subItem.mapType : null,
                        selectName: subItem.selectName ? subItem.selectName : null,
                        selectType: subItem.selectType ? subItem.selectType : null,
                        selectOption: subItem.selectOption ? subItem.selectOption : null,
                        willChangeOther: subItem['willChangeOther'] ? subItem['willChangeOther'] : [],
                        willChangeSelf: subItem['willChangeSelf'] ? subItem['willChangeSelf'] : null,
                        willChangeParent: subItem['willChangeParent'] ? subItem['willChangeParent'] : []
                    };
                    list.push(obj);
                });
        });
    return list;
}

export function getHandleType(handleObj, allMap) {
    let type = 'string';
    if (handleObj && handleObj.selectType) {
        if (handleObj.selectType === 'service') {
            if (handleObj.value && handleObj.selectName) {
                let mapItem =
                    allMap &&
                    allMap[handleObj.selectName] &&
                    handleObj.value &&
                    allMap[handleObj.selectName].filter((item) => item.name === handleObj.value)[0];
                type = mapItem && mapItem['type'] ? mapItem['type'].toLowerCase() : 'string';
            }
        }
    }
    return type;
}

export const setTitle = ({ option = [] }) => {
    let dom = option;

    const { name: value, metricArea } = option?.[0]?.props?.data || {};
    const code = value?.split?.('_')?.[1];
    const tab = metricArea === 'EDIT' ? 2 : 1;
    let linkPath = null;
    if (value?.includes('offlinezb')) {
        linkPath = `/index/offIndexManage?featureSetCode=${code}&currentTab=${tab}`;
    } else if (value?.includes('salaxyzb')) {
        linkPath = `/index/realtime?metricCodes=${code}&currentTab=${tab}`;
    } else if (value?.includes('yuntuoffline')) {
        // 参数没有 metricArea， 默认运行区， tab = 1
        linkPath = `/index/orion?metricCodes=${code}&currentTab=1`;
    }

    if (linkPath) {
        dom = (
            <a
                className="link-zb"
                onClick={(e) => {
                    e.preventDefault();
                    history.push(linkPath);
                }}>
                {dom}
            </a>
        );
    }

    return dom;
};
