import { useState, useEffect } from 'react';
import { replayTaskAPI } from '@/services';

/**
 * 获取批量任务列表
 * @param  appCode // 渠道
 * @param  orgCode // 机构
 * @param  policyUuid //策略uuid
 * @param {*} serviceType //服务类型
 * @returns
 */
const getTaskList = (params) => {
    const [taskList, setTaskList] = useState([]);
    useEffect(() => {
        replayTaskAPI.getSelectList({ ...params }).then((res) => {
            const { data } = res || {};
            let arr = [];
            if (data && data.length) {
                arr = formatData(data);
            }
            setTaskList(arr);
        });
    }, [params.appCode, params.businessType, params.policyUuid, params.businessType]);

    return taskList;
};

const formatData = (taskList) => {
    taskList.map((i) => {
        i.batchInfoList = i.batchInfoList.map((item) => {
            let sortNOsArr = item?.sortNos?.split(',')?.sort((a, b) => a - b) || [];
            sortNOsArr = sortNOsArr.map((v) => ({
                name: v,
                value: v
            }));
            return {
                ...item,
                sortNOsList: sortNOsArr
            };
        });
    });
    return taskList || [];
};

export default getTaskList;
