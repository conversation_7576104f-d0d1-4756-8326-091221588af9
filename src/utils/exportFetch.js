import I18N from '@/utils/I18N';
import { message } from 'tntd';

const defaultConfig = {
    successText: I18N.utils.exportfetch.xiaZaiChengGong,
    errorText: I18N.utils.exportfetch.caoZuoShiBai,
    success: () => {},
    error: () => {}
};

function exportFetch(url, data, opts = defaultConfig) {
    opts = { ...defaultConfig, ...opts };
    const { successText, errorText, success, error } = opts;
    let options = {
        headers: {
            accept: '*/*',
            'cache-control': 'no-cache',
            'content-type': 'application/json',
            pragma: 'no-cache',
            'X-Cf-Random': sessionStorage.getItem('_csrf_')
        },
        referrerPolicy: 'strict-origin-when-cross-origin',
        method: opts?.method || 'GET',
        credentials: 'include'
    };
    options.method = options.method ? options.method : 'GET';
    if (options.method === 'POST') {
        options.body = JSON.stringify(data);
    }

    return fetch(url, options).then((res) => {
        if (res.ok) {
            res.blob().then((blob) => {
                if (res.headers.get('content-disposition')) {
                    try {
                        let filename = res.headers.get('content-disposition').split(';')[1].split('=')[1] || '';
                        filename = filename ? decodeURI(filename) : I18N.utils.exportfetch.wenJian;
                        if ('download' in document.createElement('a')) {
                            let a = document.createElement('a');
                            a.style.display = 'none';
                            let url = window.URL.createObjectURL(blob);
                            a.href = url;
                            a.download = filename.replace(/"/g, '');
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                        } else {
                            navigator.msSaveBlob(blob);
                        }
                        successText && message.success(successText);
                        success && success();
                    } catch (e) {
                        errorText && message.error(errorText);
                        error && error();
                        console.log('response header content-disposition miss!');
                    }
                } else {
                    console.log('response header content-disposition miss!');
                }
            });
        } else {
            errorText && message.error(errorText);
            error && error();
        }
    });
}

export default exportFetch;

/**
 * @description: 下载文件请求
 * @param {*} url
 * @param {*} options
 * @param {*} reportName
 */

export function downloadFileHandle(url, options, reportName, fileType) {
    // url = Config.apiPrefix + url;
    options.headers = {
        accept: '*/*',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        pragma: 'no-cache',
        'X-Cf-Random': sessionStorage.getItem('_csrf_')
    };
    if ((!!window.ActiveXObject || 'ActiveXObject' in window) && options.method && options.method.toUpperCase() === 'GET') {
        // ie兼容 阻止get请求缓存
        options.headers = {
            ...options.headers,
            'Cache-Control': 'no-cache',
            Pragma: 'no-cache'
        };
    }
    return (
        fetch(url, Object.assign(options, { credentials: 'include' }))
            // .then(checkAuthStatus)
            .then((res) => {
                const contentType = res.headers.get('content-type');
                if (contentType && contentType.toLocaleLowerCase().includes('application/json')) {
                    return res.json();
                }
                return res.blob();
            })
            .then((data) => {
                if (typeof data === 'object' && data.hasOwnProperty('success')) {
                    if (!data.success && (data.message || data.msg)) {
                        message.error(data.message || data.msg);
                    }
                    return data;
                }
                const blob = data;

                // for IE
                if (window.navigator && window.navigator.msSaveOrOpenBlob) {
                    window.navigator.msSaveOrOpenBlob(blob, `${reportName}.${fileType || 'pdf'}`);
                } else {
                    // for Non-IE (chrome, firefox etc.)
                    var a = document.createElement('a');
                    document.body.appendChild(a);
                    var url = URL.createObjectURL(blob);
                    a.href = url;
                    a.download = `${reportName}.${fileType || 'pdf'}`;
                    a.click();
                    a.remove();
                    URL.revokeObjectURL(url);
                }
            })
            .catch((err) => {
                console.error(err);
            })
    );
}
