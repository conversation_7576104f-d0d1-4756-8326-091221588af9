import I18N from '@/utils/I18N';
import { getAppStore } from '../app';
import { PolicyConstants } from '@/constants';

const getDecisionsGroup = (rootNode, arr) => {
    if (!rootNode.childNodes || rootNode.childNodes.length <= 0) {
        arr = arr.push(rootNode);
        return;
    }
    rootNode.childNodes.forEach((item) => {
        return getDecisionsGroup(item, arr);
    });
};

const getChildren = (node, dad, table) => {
    const isEnd = Object.values(dad)?.find((v) => v.type === '2');
    if (node.type === 'decision' || isEnd || (!isEnd && !node.childNodes?.length)) {
        table.push(dad);
        return;
    }
    if (Object.prototype.toString.call(node.key) === '[object Object]') {
        node.key = node.key.name;
    }
    let children = node.childNodes;
    if (children && children.length > 0) {
        let newDad = copy(dad);
        if (Object.keys(node.conditionsGroup).length <= 0 || !node.conditionsGroup.conditions) {
            newDad[node.key] = {
                conditions: null,
                type: '2',
                displayName: I18N.utils.modeltool.qiTa
            };
        } else {
            newDad[node.key] = {
                conditionsDesc: node.conditionsGroup.conditionsDesc,
                connector: node.conditionsGroup.connector,
                conditions: node.conditionsGroup.conditions,
                type: '0'
            };
        }

        children.forEach((child) => {
            getChildren(child, newDad, table);
        });
    }
};

const getDataSource = (rightItems, conditionsGroup, decisionGroup, serviceFieldListObj, ruleAndIndexFieldList) => {
    let dataSource = [];
    conditionsGroup.forEach((conditionItem, index) => {
        let dataItem = {};
        rightItems.forEach((item) => {
            if (conditionItem[item.name]) {
                let name = null;
                if (conditionItem[item.name]?.type === '2') {
                    name = {
                        displayName: I18N.utils.modeltool.qiTa,
                        description: I18N.utils.modeltool.qiTa,
                        dataType: item.dataType,
                        type: '2'
                    };
                } else {
                    name = {
                        displayName: spliceStr(
                            {
                                connector: conditionItem[item.name].connector,
                                conditions: conditionItem[item.name].conditions
                            },
                            item.dataType,
                            item.name,
                            serviceFieldListObj,
                            ruleAndIndexFieldList
                        ),
                        description: conditionItem[item.name].conditionsDesc,
                        dataType: item.dataType
                    };
                    conditionItem[item.name].displayName = `${item.name}_${name.displayName}`;
                }
                dataItem[item.name] = name;
            } else {
                dataItem[item.name] = {
                    dataType: item.dataType
                };
            }
        });
        dataItem['key'] = String(index);
        dataItem['decision'] = spliceDecisionStr(decisionGroup[index].decisions || [], serviceFieldListObj, ruleAndIndexFieldList);
        decisionGroup[index].displayName = dataItem['decision'];
        dataSource.push(dataItem);
    });

    return dataSource;
};

const spliceDecisionStr = (decision, serviceFieldListObj, ruleAndIndexFieldList) => {
    const store = getAppStore();
    let dealTypes = store.getState().decisionTable?.dealTypes;
    let decisions = decision.slice();
    let resultStr = '';
    let dealType = decisions.find((item) => item.operatorType === 'dealType');
    let dealIndex = decisions.findIndex((item) => item.operatorType === 'dealType');
    if (dealType !== undefined) {
        let field = dealTypes.find((i) => i.name === dealType.value);
        resultStr += field ? field.dName : I18N.utils.modeltool.buXiang;
        decisions.splice(dealIndex, 1);
    }
    if (decisions.length > 0 && resultStr !== '') {
        resultStr += '，';
    }
    decisions.forEach((item, index) => {
        let field = (serviceFieldListObj ? serviceFieldListObj[item.fieldType] : ruleAndIndexFieldList).find(
            (i) => i.name === item.fieldName
        );
        /* lang: 设置**字段为** */
        if (field) {
            let value = item.value;
            if (field.dataType === 'enum' && field.fieldInfo) {
                const fieldInfo = JSON.parse(field.fieldInfo);
                const obj = fieldInfo.find((k) => k.value === item.value);
                if (obj) value = obj.description;
            }
            if (index !== decisions.length - 1) {
                resultStr += I18N.template(I18N.utils.modeltool.sheZhiFIE, {
                    val1: field.displayName,
                    val2: value
                }) + ',';
            } else {
                resultStr += I18N.template(I18N.utils.modeltool.sheZhiFIE, {
                    val1: field.displayName,
                    val2: value
                });
            }
        }
    });

    return resultStr;
};

const spliceStr = (data, type, dataKey, serviceFieldListObj, ruleAndIndexFieldList) => {
    let allList = [];
    if (serviceFieldListObj) {
        allList = serviceFieldListObj['SYSTEM_FIELD']
            .concat(serviceFieldListObj['DYNAMIC_FIELD'])
            .concat(serviceFieldListObj['INDEX_FIELD']);
    } else {
        allList = ruleAndIndexFieldList;
    }

    let currentField = allList.find((k) => k?.name === dataKey);
    // const compareKey = {
    //     '>': '大于',
    //     '<': '小于',
    //     '=': '等于',
    //     '!=': '不等于',
    //     '>=': '大于等于',
    //     '<=': '小于等于',
    //     contain: '包含',
    //     equals: '等于',
    //     prefix: '前缀',
    //     suffix: '后缀',
    //     compareValue: '比较值'
    // };
    const compareKey = Object.keys(PolicyConstants.conditionOperator).reduce((total, item) => {
        PolicyConstants.conditionOperator[item].forEach((i) => {
            if (!Object.keys(total).includes(i.name)) {
                total[i.name] = i.dName;
            }
        });
        return total;
    }, {});
    const connector = {
        all: I18N.utils.modeltool.qie,
        or: I18N.utils.modeltool.huo,
        allNot: I18N.utils.modeltool.fei
    };
    let itemCon = connector[data.connector];
    let condition = '';
    data.conditions.forEach((item, index) => {
        let compareValue = item.compareValue;
        if (currentField && currentField.dataType === 'enum') {
            const fieldInfo = currentField.fieldInfo ? JSON.parse(currentField.fieldInfo).find((k) => k.value === item.compareValue) : null;
            if (fieldInfo) {
                compareValue = fieldInfo.description;
            }
        }
        if (index !== data.conditions.length - 1) {
            condition += compareKey[item.compareKey] + (type === 'string' ? `${compareValue}` : compareValue) + itemCon;
        } else {
            condition += compareKey[item.compareKey] + (type === 'string' ? `${compareValue}` : compareValue);
        }
    });
    if (data.connector === 'allNot') {
        condition = I18N.utils.modeltool.fei + '[' + condition + ']';
    }

    return condition;
};
const copy = (obj) => {
    let newObj = {};
    for (let attr in obj) {
        newObj[attr] = obj[attr];
    }
    return newObj;
};

export { getDecisionsGroup, getChildren, getDataSource, spliceDecisionStr, spliceStr, copy };
