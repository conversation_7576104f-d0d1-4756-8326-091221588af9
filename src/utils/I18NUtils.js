import I18N, { getLang } from './I18N';
import zhCN from '../../.octopus/zh-CN';
import enUS from '../../.octopus/en-US';
import { message } from 'tntd';

let langObjEN = {};
let langObjCN = {};
const changeLangObj = (data) => {
    let obj = {};
    Object.keys(data)?.map((item) => {
        let path = item;
        let value = data[item];
        Object.keys(value)?.map((item1) => {
            let secondPath = item1;
            let value1 = value[item1];
            if (typeof value1 !== 'object') {
                changeData(obj, data, value1, path, secondPath);
                return;
            }
            Object.keys(value1)?.map((item2) => {
                let str = value1[item2];
                let thirdPath = item2;
                changeData(obj, data, str, path, secondPath, thirdPath);
            });
        });
    });
    return obj;
};
const changeData = (obj, curData, curStr, path, secondPath, thirdPath) => {
    let allPath;
    if (thirdPath) {
        allPath = path + '.' + secondPath + '.' + thirdPath;
    } else {
        allPath = path + '.' + secondPath;
    }
    let str = curStr.toLowerCase();
    let sum = obj[str];
    let singleCount = obj[allPath];
    if (!singleCount) {
        if (sum) {
            obj[str] = sum + 1;
            obj[str + '_[' + (sum + 1) + ']'] = allPath;
            obj[allPath] = sum + 1;
        } else {
            obj[str + '_[1]'] = allPath;
            obj[str] = 1;
            obj[allPath] = 1;
        }
    }

    //翻译可能会重复 需要加上数字"_[出现次数]"以便跳转时区分路径
    if (obj[str]) {
        if (thirdPath) {
            curData[path][secondPath][thirdPath] = str + '_[' + obj[allPath] + ']';
        } else {
            curData[path][secondPath] = str + '_[' + obj[allPath] + ']';
        }
    }
};

const globalClickListener = (event) => {
    if (event.metaKey && event.button === 0) {
        let text =
            event.target.innerText || event.target.placeholder || event.target?.parentElement?.parentElement?.childNodes[1]?.innerHTML;
        let obj;
        if (getLang() === 'cn') {
            obj = langObjCN;
        } else {
            obj = langObjEN;
        }

        const keyword = encodeURIComponent(obj[text.toLowerCase()]);
        if (obj[text.toLowerCase()]) {
            window.open('https://sinan.tongdun.me/i18n/15176?code=noah&keyword=' + keyword);
        } else {
            message.warning('跳转失败，可能存在字符串拼接');
        }
    }
};
let callback;
export const init = (fun) => {
    if (!callback) {
        callback = fun;
    }
    const isShowKey = sessionStorage.getItem('isShowKey') === '1';
    if (!I18N) {
        return;
    }
    let zh_CN = '';
    let en_US = '';
    if (localStorage.getItem('I18NController') === '1') {
        //请求司南线上中文翻译 司南项目id:projectId 和 应用code:code需要根据项目修改
        const chineseRequest = fetch('https://sinan.tongdun.me/api/i18n/source?projectId=15176&code=noah&module=中文', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        })
            .then((response) => response.json())
            .then((res) => {
                if (res?.success) {
                    if (isShowKey) {
                        langObjCN = changeLangObj(res?.data);
                        zh_CN = res?.data;
                    } else {
                        zh_CN = res.data;
                    }
                }
            });
        //请求司南线上英文翻译 司南项目id:projectId 和 应用code:code需要根据项目修改
        const englishRequest = fetch('https://sinan.tongdun.me/api/i18n/source?projectId=15176&code=noah&module=英文', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        })
            .then((response) => response.json())
            .then((res) => {
                if (res?.success) {
                    if (isShowKey) {
                        langObjEN = changeLangObj(res?.data);
                        en_US = res?.data;
                    } else {
                        en_US = res.data;
                    }
                }
            });

        Promise.all([chineseRequest, englishRequest]).then(() => {
            I18N.setMetas({
                cn: zh_CN,
                en: en_US
            });
            callback();
            return false;
        });
    } else {
        I18N.setMetas({
            cn: zhCN,
            en: enUS
        });
        callback();
    }
    // 添加全局监听节点的函数
    if (isShowKey) {
        document.removeEventListener('mousedown', globalClickListener);
        document.addEventListener('mousedown', globalClickListener);
    }
};
