import I18N from '@/utils/I18N';
import fetch from 'dva/fetch';
import { message } from 'tntd';
import { routerRedux } from 'dva/router';
import queryString from 'query-string';
import { searchToObject } from '@/utils/utils';
import { getAppStore } from '../app';
import Config from '../common/config';
import { getHeader, getUrl } from '../services/common';
const codeMessage = {
    200: I18N.utils.request.fuWuQiChengGong,
    201: I18N.utils.request.xinZengHuoXiuGai,
    202: I18N.utils.request.yiGeQingQiuYi,
    204: I18N.utils.request.shanChuShuJuCheng,
    400: I18N.utils.request.faChuDeQingQiu2,
    401: I18N.utils.request.yongHuMeiYouQuan,
    403: I18N.utils.request.yongHuDeDaoShou,
    404: I18N.utils.request.faChuDeQingQiu,
    406: I18N.utils.request.qingQiuDeGeShi,
    410: I18N.utils.request.qingQiuDeZiYuan,
    422: I18N.utils.request.dangChuangJianYiGe,
    500: I18N.utils.request.fuWuQiFaSheng,
    502: I18N.utils.request.wangGuanCuoWu,
    503: I18N.utils.request.fuWuBuKeYong,
    504: I18N.utils.request.wangGuanChaoShi
};

function checkStatus(response) {
    if (response.status >= 200 && response.status < 300) {
        return response;
    }
    const errortext = codeMessage[response.status] || response.statusText;
    // message.error(`${errortext}`);
    const error = new Error(errortext);
    error.name = response.status;
    error.response = response;
    throw error;
}

/**
 * Requests a URL, returning a promise.
 *
 * @param  {string} url       The URL we want to request
 * @param  {object} [options] The options we want to pass to "fetch"
 * @return {object}           An object containing either "data" or "err"
 */
export default function request(url, options = {}, noApiPrefix) {
    const { notify = true } = options;
    const defaultOptions = {
        credentials: 'include'
    };
    const newOptions = {
        headers: getHeader(),
        ...defaultOptions,
        ...options,
        noApiPrefix
    };

    if (newOptions.method === 'POST' || newOptions.method === 'PUT' || newOptions.method === 'DELETE') {
        if (!(newOptions.body instanceof FormData)) {
            newOptions.headers = {
                Accept: 'application/json',
                'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8',
                ...newOptions.headers
            };

            if (options?.headers && options?.headers['Content-Type'] === 'application/json') {
                newOptions.body = JSON.stringify(newOptions.body);
            } else {
                newOptions.body = queryString.stringify(newOptions.body);
            }
            // newOptions.body = JSON.stringify(newOptions.body);
            // newOptions.body = queryString.stringify(newOptions.body);
        } else {
            // newOptions.body is FormData
            newOptions.headers = {
                Accept: 'application/json',
                'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8',
                ...newOptions.headers
            };
        }
    }

    url = noApiPrefix ? url : Config.apiPrefix + url;

    if (noApiPrefix === '/mockApi') {
        url = noApiPrefix + url;
    }

    if ((!!window.ActiveXObject || 'ActiveXObject' in window) && newOptions.method && newOptions.method.toUpperCase() === 'GET') {
        // ie兼容 阻止get请求缓存
        newOptions.headers = {
            ...newOptions.headers,
            'Cache-Control': 'no-cache',
            Pragma: 'no-cache'
        };
    }

    if (getAppStore) {
        // 获取当前机构
        const globalState = getAppStore()?.getState().global || {};
        const { currentOrgCode } = globalState;
        // 查询类接口传右上角用户选择的机构编码
        let queryParams = url.split('?');
        if (queryParams && queryParams.length > 1) {
            queryParams = searchToObject(`?${queryParams[1]}`);
            if (queryParams && !queryParams.hasOwnProperty('orgCode')) {
                if (Object.keys(queryParams)?.length) {
                    url = `${url}&orgCode=${currentOrgCode}`;
                } else {
                    if (!url.includes('?')) {
                        url += '?';
                    }
                    url = `${url}orgCode=${currentOrgCode}`;
                }
            }
        }
    }

    return fetch(url, newOptions)
        .then(checkStatus)
        .then((response) => {
            return response.json();
        })
        .then(notify ? checkResponse : (res) => res)
        .catch((e) => {
            const { dispatch } = getAppStore && getAppStore();
            const status = e.name;

            if (status === 401) {
                const { response } = e;
                try {
                    response.json().then(function (data) {
                        const { code } = data || {};

                        if (String(code) === '4011003') {
                            dispatch({
                                type: 'global/setAttrValue',
                                payload: {
                                    multiUserModal: true
                                }
                            });
                        } else {
                            dispatch({
                                type: 'login/goLogin'
                            });
                        }
                    });
                } catch (e) {
                    dispatch({
                        type: 'login/goLogin'
                    });
                }
                dispatch({
                    type: 'login/goLogin'
                });
                return;
            }
            if (status === 403) {
                if (process.env.SYS_ENV === 'development') {
                    dispatch(routerRedux.push('/noah/exception/403'));
                    return;
                }
                dispatch(routerRedux.push('/noah/exception/403'));
                return;
            }
            if (status <= 504 && status >= 500) {
                // if (process.env.SYS_ENV === 'development') {
                //     return;
                // }
                dispatch(routerRedux.push('/noah/exception/500'));
                dispatch({
                    type: 'global/setAttrValue',
                    payload: {
                        allMapReady: true,
                        workflowConfigReady: true
                    }
                });
                return;
            }
            if (status === 404) {
                if (process.env.SYS_ENV !== 'development') {
                    dispatch(routerRedux.push('/noah/exception/404'));
                }
            }
        });
}

const checkResponse = (response) => {
    if (!response) {
        return {
            success: false,
            data: {}
        };
    }
    if (!response.success) {
        const messages = response.message || response.returnMsg || response.message || I18N.utils.request.weiZhiCuoWu + response.code;
        message.error(messages.slice(0, 200));
    }

    return response;
};

/**
 * @description: 下载文件请求
 * @param {*} url
 * @param {*} options
 * @param {*} reportName
 */
export function downloadFileHandle(url, options, reportName, fileType, noApiPrefix) {
    options = {
        noApiPrefix,
        credentials: 'include',
        headers: getHeader(),
        ...options
    };
    if ((!!window.ActiveXObject || 'ActiveXObject' in window) && options.method && options.method.toUpperCase() === 'GET') {
        // ie兼容 阻止get请求缓存
        options.headers = {
            ...options.headers,
            'Cache-Control': 'no-cache',
            Pragma: 'no-cache'
        };
    }

    url = noApiPrefix ? url : Config.apiPrefix + url;

    let downFileName = `${reportName}.${fileType || 'pdf'}`;
    return fetch(url, options)
        .then((res) => {
            const contentType = res.headers.get('content-type');
            if (contentType && contentType.toLocaleLowerCase().includes('application/json')) {
                return res.json();
            }
            const disposition = res.headers.get('Content-Disposition');
            if (disposition && disposition.split('=') && !(reportName && fileType)) {
                downFileName = disposition.split('=')[1].replace(/\"/g, '');
                if (downFileName) {
                    downFileName = decodeURIComponent(downFileName);
                }
            }
            return res.blob();
        })
        .then((data) => {
            if (typeof data === 'object' && data.hasOwnProperty('success')) {
                if (!data.success && data.message) {
                    message.error(data.message);
                }
                return data;
            }
            const blob = data;
            // for IE
            if (window.navigator && window.navigator.msSaveOrOpenBlob) {
                window.navigator.msSaveOrOpenBlob(blob, `${reportName}.${fileType || 'pdf'}`);
            } else {
                // for Non-IE (chrome, firefox etc.)
                let a = document.createElement('a');
                document.body.appendChild(a);
                let url = URL.createObjectURL(blob);
                a.href = url;
                // if (reportName && fileType) {
                a.download = `${downFileName}`;
                // }
                a.click();
                a.remove();
                URL.revokeObjectURL(url);
            }
        })
        .catch(() => {
            // console.error(err);
        });
}

/**
 * @description: 导出报表
 * @param {*} url
 * @param {*} options
 * @param {*} fileName
 */
// export function downloadReport(url, options, fileName, fileType) {
// 	url = Config.apiPrefix + url;
// 	let a = document.createElement("a");
// 	a.download = `${fileName}.${fileType || "pdf"}`;
// 	a.href = url;
// 	document.body.appendChild(a);
// 	a.click();
// 	a.remove();
// }

export function downloadReport(url, options, fileName, fileType) {
    url = Config.apiPrefix + url;
    let downFileName = `${fileName}.${fileType || 'pdf'}`;
    if ((!!window.ActiveXObject || 'ActiveXObject' in window) && options.method && options.method.toUpperCase() === 'GET') {
        // ie兼容 阻止get请求缓存
        options.headers = {
            ...options.headers,
            'Cache-Control': 'no-cache',
            Pragma: 'no-cache'
        };
    }
    return fetch(url, { ...options, credentials: 'include' })
        .then((res) => {
            const disposition = res.headers.get('Content-Disposition');
            // console.log(disposition);
            if (disposition && disposition.split('=') && !(fileName && fileType)) {
                downFileName = disposition.split('=')[1].replace(/\"/g, '');
                if (downFileName) {
                    downFileName = decodeURIComponent(downFileName);
                }
            }
            return res.blob();
        })
        .then((blob) => {
            // for IE
            if (window.navigator && window.navigator.msSaveOrOpenBlob) {
                window.navigator.msSaveOrOpenBlob(blob, `${downFileName}`);
            } else {
                // for Non-IE (chrome, firefox etc.)
                let a = document.createElement('a');
                document.body.appendChild(a);
                let url = URL.createObjectURL(blob);
                a.href = url;
                a.download = `${downFileName}`;
                a.click();
                a.remove();
                URL.revokeObjectURL(url);
            }
        })
        .catch(() => {
            // console.error(err);
        });
}

/**
 * @description: 下载5个进件文件请求
 * @param {*} url
 * @param {*} options
 * @param {*} reportName
 */
export function downloadAllFile(url, options) {
    options = {
        ...options,
        credentials: 'include',
        headers: getHeader()
    };
    if ((!!window.ActiveXObject || 'ActiveXObject' in window) && options.method && options.method.toUpperCase() === 'GET') {
        // ie兼容 阻止get请求缓存
        options.headers = {
            ...options.headers,
            'Cache-Control': 'no-cache',
            Pragma: 'no-cache'
        };
    }
    return fetch(url, options)
        .then((res) => res.blob())
        .then((blob) => {
            // for IE
            if (window.navigator && window.navigator.msSaveOrOpenBlob) {
                window.navigator.msSaveOrOpenBlob(blob, 'report.zip');
            } else {
                // for Non-IE (chrome, firefox etc.)
                let a = document.createElement('a');
                document.body.appendChild(a);
                let url = URL.createObjectURL(blob);
                a.href = url;
                a.download = 'report.zip';
                a.click();
                a.remove();
                URL.revokeObjectURL(url);
            }
        })
        .catch(() => {
            // console.error(err);
        });
}

export function PostForm(url, type, datas, noApiPrefix) {
    url = noApiPrefix ? url : Config.apiPrefix + url;
    const formData = new FormData();
    Object.keys(datas).forEach((key) => {
        formData.append(key, datas[key]);
    });
    return fetch(url, {
        method: type,
        credentials: 'include',
        cache: 'no-cache',
        body: formData,
        headers: getHeader()
    })
        .then((res) => {
            return res.json();
        })
        .then((res) => {
            return res;
        });
}

request.get = function (url, params) {
    return request(getUrl(url, params), {
        method: 'GET',
        headers: getHeader()
    });
};
request.post = function (url, params) {
    return request(getUrl(url), {
        method: 'POST',
        headers: getHeader(),
        body: params,
        notify: true
    });
};
