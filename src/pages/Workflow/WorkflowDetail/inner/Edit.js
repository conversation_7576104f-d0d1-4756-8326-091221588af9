import I18N from '@/utils/I18N';
import { useEffect, useState } from 'react';
import { connect } from 'dva';
import { message, Transfer, Button, Select, Tag, Popover, Icon, Breadcrumb } from 'tntd';
import InfoSection, { InfoTitle } from '@/components/InfoSection';
import TdTag from '@/components/TdTag';

import { workflowAPI } from '@/services';
import { isAccountBusinessType } from '@/utils/utils';
import './index.less';
import PublishModal from '../../WorkflowList/modal/PublishModal';
const ButtonGroup = Button.Group;
const { Option } = Select;
const Edit = (props) => {
    const { history, globalStore, workflowListStore } = props;
    const { appMap, orgMap, allMap } = globalStore;
    // mode
    // 0 简易模式
    // 1 流模式
    const { editVersionParams } = workflowListStore;
    const { policyRecord } = editVersionParams;
    const { policyVersionUuid, policyUuid, policyStatus, orgCode, appCode } = props.match.params;
    const [data, setData] = useState([]);
    const [curPolicyVersionUuid, setCurPolicyVersionUuid] = useState();
    const [publishData, setPublishData] = useState();
    const [status, setStatus] = useState();
    const [versions, setVersions] = useState([]);
    const [ruleList, setRuleList] = useState([]);
    const [targetKeys, setTargetKeys] = useState([]);
    const [flag, setFlag] = useState(true);

    const { ruleSetMap = {} } = allMap || {};

    const [selectedKeys, setSelectedKeys] = useState([]);
    const [publishVisible, setPublishVisible] = useState(false);
    const [publishStatus, setPublishStatus] = useState(false);

    // const [versionData,setVersionData] =useState
    const { policyMode, policyModeName } = data;
    const versionStatusMap = {
        1: I18N.workflowdetail.inner.yiZanCun,
        2: I18N.workflowdetail.inner.yiBaoCun,
        3: I18N.workflowdetail.inner.daiShenHe,
        4: I18N.workflowdetail.inner.daiFuHe,
        5: I18N.workflowdetail.inner.yiShangXian,
        6: I18N.workflowdetail.inner.yiXiaXian
    };
    const versionStatusColorMap = {
        1: 'rgba(18,187,251)',
        2: 'rgba(18,187,251)',
        3: 'rgba(247,176,54)',
        4: 'rgba(255,152,69)',
        5: 'rgba(7,199,144)',
        6: 'rgba(94,112,146)'
    };
    const versionStatusColorRgbMap = {
        1: 'rgba(18,187,251,0.1)',
        2: 'rgba(18,187,251,0.1)',
        3: 'rgba(247,176,54,0.1)',
        4: 'rgba(255,152,69,0.1)',
        5: 'rgba(7,199,144,0.1)',
        6: 'rgba(94,112,146,0.1)'
    };

    useEffect(() => {
        if (policyVersionUuid) {
            setCurPolicyVersionUuid(policyVersionUuid);
            getDetail(policyVersionUuid, orgCode);
        }
    }, [policyVersionUuid, orgCode]);
    useEffect(() => {
        if (policyUuid) {
            getPolicyList();
            getPublishData();
            getPublishStatus();
        }
    }, [policyUuid]);

    useEffect(() => {
        if (ruleList?.length > 0 && targetKeys?.length > 0 && flag) {
            let arr = ruleList;
            targetKeys?.map((item) => {
                if (!ruleList.find((item1) => item1.key === item)) {
                    let obj = {
                        key: item,
                        title: ruleSetMap[item]?.ruleSetName || item,
                        delete: true,
                        disabled: true
                    };
                    arr.push(obj);
                }
            });
            setRuleList([...arr]);
            setFlag(false);
        }
    }, [ruleList, targetKeys, flag]);

    useEffect(() => {
        setStatus(data.status);
    }, [data]);

    useEffect(() => {
        getAllRule();
    }, []);

    useEffect(() => {
        getStatus(curPolicyVersionUuid);
    }, [versions]);

    const getPublishStatus = () => {
        workflowAPI.getFilterServerList({ areaType: 1, uuid: policyUuid }).then((res) => {
            if (res?.data) {
                setPublishStatus(res?.data?.status);
            }
        });
    };

    const getStatus = () => {
        versions.map((item) => {
            if (item.uuid === curPolicyVersionUuid) {
                setStatus(item.status);
            }
        });
    };

    const changeVersion = (uuid) => {
        setCurPolicyVersionUuid(uuid);
        versions.map((item) => {
            if (item.uuid === uuid) {
                setStatus(item.status);
            }
        });
        workflowAPI.getDetail({ policyVersionUuid: uuid }).then((res) => {
            if (res.success) {
                setTargetKeys(res?.data.ruleSetUuidList);
            }
        });
    };

    const getAllRule = () => {
        // 获取当前渠道下所有规则集
        let params = {
            orgCode,
            appCode,
            withImport: true
        };
        workflowAPI.getAllRuleSet(params).then((res) => {
            if (res.success && res) {
                let arr = [];
                res?.data.map((item) => {
                    let obj = {};
                    obj.key = item.ruleSetUuid;
                    obj.title = item.ruleSetName;
                    obj.status = item?.ruleSetStatus || item?.status;

                    arr.push(obj);
                });
                setRuleList(arr);
            }
        });
    };
    const getDetail = (curPolicyVersionUuid, orgCode) => {
        let params = {
            orgCode,
            policyVersionUuid: curPolicyVersionUuid
        };
        // 详情信息
        workflowAPI.getDetail(params).then((res) => {
            if (res.success && res) {
                setTargetKeys(res?.data.ruleSetUuidList);
                setData(res.data);
            }
        });
    };
    // 版本信息
    const getPolicyList = () => {
        workflowAPI.getPolicyList({ policyUuid }).then((res) => {
            if (res.success && res) {
                setVersions(res.data);
            }
        });
    };
    const handleSelectChange = async (sourceSelectedKeys, targetSelectedKeys) => {
        const businessType = data?.policyBusinessType;
        if (sourceSelectedKeys?.length && isAccountBusinessType(businessType)) {
            const policyCheck = await workflowAPI.decisionflowCheck({
                ruleSetUuid: sourceSelectedKeys.slice(-1)
            });
            if (policyCheck.success) {
                setSelectedKeys([...sourceSelectedKeys, ...targetSelectedKeys]);
            } else {
                return;
            }
        }
        setSelectedKeys([...sourceSelectedKeys, ...targetSelectedKeys]);
    };
    const handleChange = (nextTargetKeysPre) => {
        let nextTargetKeys = nextTargetKeysPre;
        if (nextTargetKeysPre?.length) {
            nextTargetKeys = Array.from(new Set(nextTargetKeysPre));
        }
        setTargetKeys(nextTargetKeys);
        //每一次操作以后都变成暂存状态
        let params = {
            policyVersionUuid: curPolicyVersionUuid,
            ruleSetUuidList: nextTargetKeys && nextTargetKeys.join(','),
            type: 1
        };
        workflowAPI.updatePolicyVersion(params).then(async (res) => {
            // if (res.success && !isAuto) {
            //     message.success(res.message);
            // }
            await changeVersion(curPolicyVersionUuid);
            await getPolicyList();
            await getPublishData();
        });
    };
    const saveRule = (value, isAuto) => {
        // 1暂存 2保存
        if ((!targetKeys || targetKeys.length === 0) && value === 2) {
            message.warning(I18N.workflowdetail.inner.qingZhiShaoShiYong);
            // setPublishVisible(true);
        } else {
            let params = {
                policyVersionUuid: curPolicyVersionUuid,
                ruleSetUuidList: targetKeys && targetKeys.join(','),
                type: value
            };
            workflowAPI.updatePolicyVersion(params).then(async (res) => {
                if (res.success && !isAuto) {
                    message.success(res.message);
                }
                isAuto && setPublishVisible(true);
                await changeVersion(curPolicyVersionUuid);
                await getPolicyList();
                await getPublishData();
            });
        }
    };

    const getPublishData = () => {
        let params = {
            policyUuid,
            status: '2,5,6'
        };
        workflowAPI.getPolicyList(params).then((res) => {
            setPublishData({
                mainList: res.data,
                versionList: res.data,
                splitConfig: [{ version: '', ratio: '' }]
            });
        });
    };
    const content = (
        <div>
            <p>
                {I18N.workflowdetail.inner.yeWuLeiXing}：{data?.policyBusinessTypeName}
            </p>
            <p>
                {I18N.workflowdetail.inner.shiJianLeiXing}：{data?.childrenFlow !== 1 ? data?.eventTypeName : '-'}
            </p>
            <p>
                {I18N.workflowdetail.inner.suoShuJiGou}：{orgMap[data.orgCode]?.name}
            </p>
            <p>
                {I18N.workflowdetail.inner.suoShuQuDao}：{appMap[data?.appCode]}
            </p>
            <p>
                {I18N.workflowdetail.inner.ceLueZhiXingMo}：{data?.policyModeName}
            </p>
        </div>
    );
    const cancelHandle = () => {
        setPublishVisible(false);
    };

    const renderItem = (record) => {
        ruleList?.map((item) => {});
        return (
            <>
                <span>
                    {record.delete && <Tag color="orange">{I18N.workflowdetail.inner.zhuangTaiYiChang}</Tag>}
                    <TdTag data={record} type="ruleSet" />
                    {record.title}
                </span>
                <span />
                {record?.delete && (
                    <span
                        className="delete-icon"
                        onClick={() => {
                            let arr = targetKeys;
                            let index = arr.findIndex((item) => item === record.key);
                            arr.splice(index, 1);
                            // setTargetKeys([...arr]);
                            handleChange([...arr]);
                        }}>
                        <Icon type="delete" />
                    </span>
                )}
            </>
        );
    };
    return (
        <>
            <div className="page-global-header">
                <Breadcrumb className="bread-container" separator=">">
                    <Breadcrumb.Item>
                        <span
                            style={{ cursor: 'pointer' }}
                            onClick={() => {
                                history.push('/noah/policyManage?currentTab=2');
                            }}>
                            {I18N.rulesetlist.inner.fanHui}
                        </span>
                    </Breadcrumb.Item>
                    <Breadcrumb.Item>{I18N.workflowdetail.inner.ceLueXiangQing}</Breadcrumb.Item>
                </Breadcrumb>
            </div>

            <div className="page-global-body">
                <div className="page-global-body-main">
                    <div className="view-body">
                        <InfoSection className="mt20">
                            <InfoTitle title={I18N.workflowdetail.inner.ceLuePeiZhiXin} />
                            <div className="version-list">
                                <span>{I18N.workflowdetail.inner.banBenHao}</span>
                                <Select
                                    style={{ width: '100px' }}
                                    defaultValue={policyVersionUuid}
                                    onChange={(value) => {
                                        history.push(
                                            `/noah/policyManage/Edit/${value}/${policyUuid}/${policyStatus}/${orgCode}/${appCode}`
                                        );
                                        // changeVersion(value);
                                    }}>
                                    {versions.map((item) => {
                                        if (item.status === 1 || item.status === 2) {
                                            return (
                                                <Option value={item.uuid}>
                                                    V{item.version}
                                                    {item.type && <span>{I18N.workflowdetail.inner.zhuBanBen}</span>}
                                                </Option>
                                            );
                                        }
                                    })}
                                </Select>
                                {status && (
                                    <Tag
                                        style={{
                                            marginLeft: '6px',
                                            background: versionStatusColorRgbMap[status],
                                            color: versionStatusColorMap[status],
                                            borderColor: versionStatusColorMap[status]
                                        }}
                                        className="version-tag">
                                        {versionStatusMap[status]}
                                    </Tag>
                                )}
                                <Popover placement="bottom" title={I18N.workflowdetail.inner.gengDuoXinXi} content={content}>
                                    <span style={{ marginLeft: '10px', verticalAlign: 'middle', cursor: 'pointer' }}>
                                        <Icon type="info-circle" style={{ position: 'relative', top: '1px', marginRight: '3px' }} />
                                        {I18N.workflowdetail.inner.gengDuo}
                                    </span>
                                </Popover>
                            </div>
                            <div className="audit-detail-item clearfix">
                                <div className="right-chart">
                                    {policyMode === 1 ? (
                                        <>
                                            <ButtonGroup>
                                                <Button
                                                    onClick={() => {
                                                        saveRule(1);
                                                    }}>
                                                    {I18N.workflowdetail.inner.zanCun}
                                                </Button>
                                                <Button
                                                    onClick={() => {
                                                        saveRule(2);
                                                    }}>
                                                    {I18N.workflowdetail.inner.baoCun}
                                                </Button>
                                                <Button
                                                    type="primary"
                                                    disabled={publishStatus === 2 || publishStatus === 3}
                                                    onClick={() => {
                                                        saveRule(2, true);
                                                    }}>
                                                    {I18N.workflowdetail.inner.faBu}
                                                </Button>
                                            </ButtonGroup>
                                            <Transfer
                                                className={isAccountBusinessType(data?.policyBusinessType) ? 'real-rule-set' : ''}
                                                render={renderItem}
                                                dataSource={ruleList}
                                                titles={[
                                                    I18N.workflowdetail.inner.weiShiYongGuiZe,
                                                    I18N.workflowdetail.inner.yiShiYongGuiZe
                                                ]}
                                                targetKeys={targetKeys}
                                                selectedKeys={selectedKeys}
                                                onSelectChange={handleSelectChange}
                                                onChange={handleChange}
                                            />
                                        </>
                                    ) : (
                                        <></>
                                    )}
                                </div>
                            </div>
                        </InfoSection>
                    </div>
                </div>
            </div>

            <PublishModal
                record={{
                    ...(policyRecord || {}),
                    businessType: data?.policyBusinessType
                }}
                history={history}
                publishData={publishData}
                visible={publishVisible}
                policyUuid={policyUuid}
                onCancel={cancelHandle}
                destroyOnClose
            />
        </>
    );
};
export default connect((state) => ({
    globalStore: state.global,
    workflowListStore: state.workflowList
}))(Edit);
