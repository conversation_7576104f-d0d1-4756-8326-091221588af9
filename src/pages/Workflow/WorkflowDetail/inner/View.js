import I18N from '@/utils/I18N';
import { useEffect, useState } from 'react';
import { Spin, Select, Tag, Breadcrumb } from 'tntd';
import { connect } from 'dva';
import InfoSection, { InfoTitle, InfoTable, InfoTd } from '@/components/InfoSection';
import { workflowAPI } from '@/services';
import './index.less';
import MMEditor from '@/components/workflow/MMEditor';
import TdTag from '@/components/TdTag';

const { Option } = Select;
const View = (props) => {
    const { history, dispatch, globalStore } = props;
    const { appMap, orgMap, allMap } = globalStore;
    const { ruleSetMap = {} } = allMap || {};
    const [data, setData] = useState({});
    const [ruleMap, setRuleMap] = useState();
    const [langLoading, setLangLoading] = useState(false);
    const {
        policyBusinessTypeName,
        policyName,
        mode,
        policyModeName,
        policyMode,
        graphJson,
        description,
        policyCode,
        eventTypeName,
        scenario,
        appCode,
        orgCode,
        childrenFlow
    } = data || {};
    const { policyVersionUuid, policyUuid, type = 1 } = props.match.params;
    const [versions, setVersions] = useState([]);
    const [status, setStatus] = useState();
    const [statusName, setStatusName] = useState();

    useEffect(() => {
        let params = {
            policyVersionUuid: props?.match?.params?.policyVersionUuid,
            areaType: type
        };
        workflowAPI.getDetail(params).then(async (res) => {
            setData(res.data);
        });
        getPolicyList();
    }, []);
    useEffect(() => {
        if (data?.status && data?.orgCode && data?.appCode) {
            setStatus(data.status);
            setStatusName(data?.statusName);
            getRuleSet();
        }
    }, [data]);

    useEffect(() => {
        if (data?.policyUuid && data?.version) {
            changeVersion();
        }
    }, [data]);
    // 初始化画布数据
    const versionStatusMap = {
        1: I18N.workflowdetail.inner.yiZanCun,
        2: I18N.workflowdetail.inner.yiBaoCun,
        3: I18N.workflowdetail.inner.daiShenHe,
        4: I18N.workflowdetail.inner.daiFuHe,
        5: I18N.workflowdetail.inner.yiShangXian,
        6: I18N.workflowdetail.inner.yiXiaXian
    };
    const versionStatusColorMap = {
        1: 'rgba(18,187,251)',
        2: 'rgba(18,187,251)',
        3: 'rgba(247,176,54)',
        4: 'rgba(255,152,69)',
        5: 'rgba(7,199,144)',
        6: 'rgba(94,112,146)'
    };
    const versionStatusColorRgbMap = {
        1: 'rgba(18,187,251,0.1)',
        2: 'rgba(18,187,251,0.1)',
        3: 'rgba(247,176,54,0.1)',
        4: 'rgba(255,152,69,0.1)',
        5: 'rgba(7,199,144,0.1)',
        6: 'rgba(94,112,146,0.1)'
    };

    const changeVersion = (uuid) => {
        versions.map((item) => {
            if (item.uuid === uuid) {
                setStatus(item.status);
                setStatusName(item?.statusName);
                setLangLoading(true);
                workflowAPI
                    .getDetail({
                        policyVersionUuid: uuid
                    })
                    .then((item) => {
                        if (item.success) {
                            const data = item.data;
                            setData(data);
                        }
                        setLangLoading(false);
                    });
            }
        });
    };

    const getRuleSet = () => {
        let params1 = {
            appCode,
            orgCode
        };
        let arr = [];
        // 获取当前渠道下所有规则集 匹配规则集
        workflowAPI.getAllRuleSet(params1).then((res1) => {
            res1?.data.map((item) => {
                let obj = {};
                data?.ruleSetUuidList?.forEach((item1) => {
                    if (item.ruleSetUuid === item1) {
                        obj.status = item.status;
                        obj.name = item.ruleSetName;
                        obj.code = item.ruleSetCode;
                        obj.key = item.ruleSetUuid;
                        arr.push(obj);
                    }
                });
            });
            //没匹配到的状态异常也要加进去
            data?.ruleSetUuidList?.map((item) => {
                if (!arr.find((item1) => item1.key === item)) {
                    let obj = {};
                    // obj.code = item;
                    // obj.name = item;
                    if (ruleSetMap[item]) {
                        obj.originalName = ruleSetMap[item]?.ruleSetName;
                        obj.code = ruleSetMap[item]?.ruleSetCode;
                    }
                    arr.push(obj);
                }
            });

            setRuleMap(arr);
        });
    };
    const getPolicyList = () => {
        let params = {
            policyUuid,
            areaType: type
        };
        workflowAPI.getPolicyList(params).then((res) => {
            if (res.success && res) {
                setVersions(res.data);
            }
        });
    };
    return (
        <>
            <div className="page-global-header">
                <Breadcrumb className="bread-container" separator=">">
                    <Breadcrumb.Item>
                        <span
                            style={{ cursor: 'pointer' }}
                            onClick={() => {
                                if (props.match.params.type === '1') {
                                    history.push('/noah/policyManage?currentTab=2');
                                } else {
                                    history.push('/noah/policyManage?currentTab=1');
                                }
                            }}>
                            {I18N.rulesetlist.inner.fanHui}
                        </span>
                    </Breadcrumb.Item>
                    <Breadcrumb.Item>{I18N.workflowdetail.inner.ceLueChaKan}</Breadcrumb.Item>
                </Breadcrumb>
            </div>
            <div className="page-global-body">
                <div className="view-body-main">
                    <div className="view-body">
                        <InfoSection className="mt20">
                            <div className="version-list">
                                <span>{I18N.workflowdetail.inner.banBenHao}</span>
                                <Select
                                    style={{ width: '100px' }}
                                    defaultValue={policyVersionUuid}
                                    onChange={(value) => {
                                        history.push(`/noah/policyManage/View/${value}/${policyUuid}/${type}`);
                                        changeVersion(value);
                                    }}>
                                    {versions.map((item) => {
                                        if (type === '2') {
                                            if (item.status === 5) {
                                                return (
                                                    <Option value={item.uuid}>
                                                        V{item.version} {item.type && <span>{I18N.workflowdetail.inner.zhuBanBen}</span>}
                                                    </Option>
                                                );
                                            }
                                        } else {
                                            return (
                                                <Option value={item.uuid}>
                                                    V{item.version} {item.type && <span>{I18N.workflowdetail.inner.zhuBanBen}</span>}
                                                </Option>
                                            );
                                        }
                                    })}
                                </Select>
                                {status && (
                                    <Tag
                                        style={{
                                            marginLeft: '6px',
                                            background: versionStatusColorRgbMap[status],
                                            color: versionStatusColorMap[status],
                                            borderColor: versionStatusColorMap[status]
                                        }}
                                        className="version-tag">
                                        {/* {versionStatusMap[status]} */}
                                        {statusName || ''}
                                    </Tag>
                                )}
                                {/* <Popover
                                    placement="bottom"
                                    title="更多信息"
                                    content={content}
                                >
                                    <span style={{ marginLeft: '10px', verticalAlign: 'middle', cursor: 'pointer' }}>
                                        <Icon type="info-circle" style={{ position: 'relative', top: '1px', marginRight: '3px' }} />
                                        更多
                                    </span>
                                </Popover> */}
                            </div>

                            <InfoTitle title={I18N.workflowdetail.inner.ceLueXiangQing} />
                            <InfoTable cols={3}>
                                <InfoTd
                                    label={I18N.workflowdetail.inner.ceLueMingCheng}
                                    value={policyName}
                                    rate={{ labelWidth: '180px' }}
                                />
                                <InfoTd label={I18N.workflowdetail.inner.ceLueBiaoZhi} value={policyCode} rate={{ labelWidth: '180px' }} />
                                <InfoTd
                                    label={I18N.workflowdetail.inner.yeWuLeiXing}
                                    value={policyBusinessTypeName}
                                    rate={{ labelWidth: '180px' }}
                                />
                                <InfoTd
                                    label={I18N.workflowdetail.inner.shiJianLeiXing}
                                    value={childrenFlow !== 1 ? eventTypeName : '-'}
                                    rate={{ labelWidth: '180px' }}
                                />
                                <InfoTd
                                    label={I18N.workflowdetail.inner.suoShuJiGou}
                                    value={data?.orgCode && orgMap[data?.orgCode].name}
                                    rate={{ labelWidth: '180px' }}
                                />
                                <InfoTd
                                    label={I18N.workflowdetail.inner.suoShuQuDao}
                                    value={appMap[data?.appCode]}
                                    rate={{ labelWidth: '180px' }}
                                />
                                <InfoTd
                                    label={I18N.workflowdetail.inner.ceLueZhiXingMo}
                                    value={policyModeName}
                                    rate={{ labelWidth: '180px' }}
                                />
                                <InfoTd
                                    className="item-part2-full"
                                    label={I18N.workflowdetail.inner.shiFouZiCeLue}
                                    value={childrenFlow === 1 ? I18N.workflowdetail.inner.shi : I18N.workflowdetail.inner.fou}
                                    rate={{ labelWidth: '180px' }}
                                />
                                <InfoTd
                                    className="item-full rate"
                                    label={I18N.workflowdetail.inner.miaoShu}
                                    value={description}
                                    rate={{ labelWidth: '180px' }}
                                />
                            </InfoTable>
                        </InfoSection>
                        {policyMode === 1 ? (
                            <InfoSection className="mt20">
                                <InfoTitle title={I18N.workflowdetail.inner.yiShiYongGuiZe} />
                                <InfoTable cols={2}>
                                    <InfoTd
                                        className="item-full rate grey"
                                        label={<span style={{ fontWeight: 600 }}>{I18N.workflowdetail.inner.guiZeJiMingCheng}</span>}
                                        rate={{ labelWidth: '23%', valueWidth: '77%' }}
                                        value={<span style={{ fontWeight: 600 }}>{I18N.workflowdetail.inner.guiZeJiBiaoZhi}</span>}
                                    />
                                </InfoTable>
                                <InfoTable cols={2}>
                                    {ruleMap &&
                                        ruleMap.map((item, index) => {
                                            return (
                                                <InfoTd
                                                    key={index}
                                                    className="item-full rate white"
                                                    label={
                                                        <>
                                                            <TdTag data={item} showSourceName={false} />
                                                            {item.name || item?.originalName}
                                                        </>
                                                    }
                                                    rate={{ labelWidth: '23%', valueWidth: '77%' }}
                                                    value={
                                                        <a
                                                            style={{ display: 'flex' }}
                                                            disabled={!item.name}
                                                            onClick={() => {
                                                                if (item.status === 'online') {
                                                                    history.push('/noah/ruleSet?code=' + item.code);
                                                                } else {
                                                                    history.push('/noah/ruleSet?currentTab=2&code=' + item.code);
                                                                }
                                                            }}>
                                                            {!item.name && (
                                                                <Tag color="orange">{I18N.workflowdetail.inner.zhuangTaiYiChang}</Tag>
                                                            )}

                                                            {item.code}
                                                        </a>
                                                    }
                                                />
                                            );
                                        })}
                                </InfoTable>
                            </InfoSection>
                        ) : (
                            <InfoSection className="mt20">
                                <InfoTitle title={I18N.workflowdetail.inner.liuChengXiangQing} />
                                <Spin spinning={langLoading} style={{ margin: '100px atuo' }}>
                                    <MMEditor
                                        type="chart"
                                        orgCode={data?.orgCode}
                                        appCode={data?.appCode}
                                        policyBusinessTypeName={data?.policyBusinessTypeName}
                                        policyBusinessType={data?.policyBusinessType}
                                        dispatch={dispatch}
                                        graphData={graphJson}
                                    />
                                </Spin>
                            </InfoSection>
                        )}
                    </div>
                </div>
            </div>
        </>
    );
};
export default connect((state) => ({
    globalStore: state.global,
    workflowListStore: state.workflowList
}))(View);
