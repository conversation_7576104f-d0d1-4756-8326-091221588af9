import I18N, { getLang } from '@/utils/I18N';
import { useEffect, useRef, useState } from 'react';
import { connect } from 'dva';
import { cloneDeep } from 'lodash';
import { Icon, message } from 'tntd';
import { formatTreeData } from '@/utils/utils';

import Overview from '@/components/Overview';
import Node from '@/components/Overview/Node';

import NodeDetailModal from '@/pages/Reference/Modal/NodeDetail';
import service from '@/pages/Workflow/WorkflowList/modal/OverviewModal/service.js';
import Operation from '@/pages/Reference/Inner/Operation';
import runtimeJS from '../../../../../../public/noah-resource/overview_library.jst';
import './index.less';

// 折叠树
const collapseTree = (d) => {
    if (d.children) {
        d._children = d.children;
        d._children.forEach(collapseTree);
        d.children = null;
    }
};

const OverView = (props) => {
    let { data, policyData = {} } = props;
    const { policyName, version } = policyData;
    const [treeData, setTreeData] = useState({});
    const [nodeData, setNodeData] = useState([]);
    const [nodeDetailVisibel, setNodeDetailVisibel] = useState(false);

    const lang = getLang();

    const refs = useRef();
    useEffect(() => {
        if (data) {
            let formatData = formatTreeData(data);
            setTreeData(formatData);
        }
    }, [data]);

    const onDownload = () => {
        const props = JSON.stringify({
            data
        });
        const html = new Blob(
            [
                `
        <html>
            <meta charset="utf-8">
            <div id="root"></div>
            <script>
            window.propsData = ${props};
            window.curLang = '${lang}';
            ${runtimeJS}
            </script>
            <script>
                window.langRender();
            </script>
        </html>
        `
            ],
            { type: 'text/html' }
        );
        const url = URL.createObjectURL(html);
        const eleLink = document.createElement('a');
        eleLink.download = `${policyName}[V${version}].html`;
        eleLink.style.display = 'none';
        eleLink.href = url;
        // 触发点击
        document.body.appendChild(eleLink);
        eleLink.click();
        // 然后移除
        document.body.removeChild(eleLink);
    };

    // 请求节点抽屉详情
    const getNodeDetail = (params) => {
        service.getNodeDetail(params).then((res) => {
            if (res.success) {
                setNodeData(res.data || []);
                setNodeDetailVisibel(true);
            }
        });
    };

    return treeData ? (
        <div className="overview-content">
            <div className="overview-title">
                {I18N.policyassets.tab.yinYongGuanXiQuan}
                <a
                    style={{
                        fontFamily: 'PingFangSC-Regular',
                        fontWeight: '400',
                        fontSize: '14px',
                        float: 'right',
                        marginRight: 70
                    }}
                    onClick={() => onDownload()}>
                    <Icon type="download" style={{ top: '21px', marginRight: '5px' }} />
                    {I18N.policyassets.tab.daoChuHTM}
                </a>
            </div>
            <Operation
                refs={refs}
                data={treeData}
                initData={{
                    expand: false
                }}
            />
            <Overview
                data={treeData}
                options={{
                    fixed: true,
                    initType: false,
                    nodeDom: ({ node, nodeToggle, id, constants, fixed }) => (
                        <Node
                            constants={constants}
                            node={node}
                            nodeToggle={nodeToggle}
                            id={id}
                            onClick={(d) => {
                                let params = {
                                    componentId: d.componentId,
                                    componentType: d.componentType
                                };
                                getNodeDetail(params);
                            }}
                            onEyeClick={(data) => {
                                let { goLink } = data;
                                if (goLink) {
                                    history.push(goLink);
                                } else {
                                    message.warning(I18N.policyassets.tab.zanWuLianJieTiao);
                                }
                            }}
                            fixed={fixed}
                        />
                    ),
                    customPosition: (node) => {
                        let { data, parent, x, y } = node;
                        let { isGroupItem } = data;
                        // 节点是实例节点有且只有一个，不用连接线
                        if (isGroupItem === false && parent?.children?.length === 1) {
                            y -= 24;
                        }
                        return [x, y];
                    }
                }}
                refs={refs}
            />
            <NodeDetailModal visible={nodeDetailVisibel} onCancel={() => setNodeDetailVisibel(false)} data={nodeData} />
        </div>
    ) : null;
};

export default connect((state) => ({
    globalStore: state.global,
    workflowListStore: state.workflowList
}))(OverView);
