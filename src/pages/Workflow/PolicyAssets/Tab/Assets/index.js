import I18N from '@/utils/I18N';
import { Ellipsis } from 'tntd';
import MMEditor from '@/components/workflow/MMEditor';
import history from '@/utils/history';

const Assets = (props) => {
    //true 首页
    const { data = {}, policyData = {}, code, policyVersionUuid, version } = props;
    const { businessType, eventType, policyCode, policyMode, policyName, policyVersion, graphJson, componentSumList = [] } = data;
    const colorMap = [
        {
            color: '#EAF4FE'
        },
        {
            color: '#FFF1E2'
        },
        {
            color: '#ECFDDF'
        },
        {
            color: '#EAF2FF'
        },
        {
            color: '#F2EAFF'
        },
        {
            color: '#facfc5'
        }
    ];
    return (
        <>
            <div className="overview-container">
                <div className="overview-title">{I18N.policyassets.tab.ceLueGaiLan}</div>
                <div className="clearfix">
                    {componentSumList.map((item, index) => {
                        return (
                            <div
                                className="item-part"
                                onClick={() => {
                                    history.push(
                                        `/noah/policyManage/assets/${code}/${policyVersionUuid}/${version}/list?componentType=${item.componentType}`
                                    );
                                }}
                                key={index}
                                style={{ background: colorMap[index]?.color }}>
                                <div className="item-count">{item?.totalCount}</div>
                                <div className="item-type">{item?.componentTypeName}</div>
                            </div>
                        );
                    })}
                </div>
            </div>
            <div className="basic-content">
                <div className="basic-title">{I18N.policyassets.tab.ceLueJiBenXin}</div>
                <div className="basic-bottom">
                    <div className="basic-container">
                        <div className="content-title">{I18N.policyassets.tab.ceLueMingCheng}：</div>
                        <div className="content-text">
                            <Ellipsis title={policyName} widthLimit={180} />
                        </div>
                    </div>
                    <div className="basic-container">
                        <div className="content-title">{I18N.policyassets.tab.ceLueBiaoZhi}：</div>
                        <div className="content-text">
                            <Ellipsis title={policyCode} widthLimit={180} />
                        </div>
                    </div>
                    <div className="basic-container">
                        <div className="content-title">{I18N.policyassets.tab.ceLueBanBen}：</div>
                        <div className="content-text">
                            <Ellipsis title={'V' + policyVersion} widthLimit={180} />
                        </div>
                    </div>
                    <div className="basic-container">
                        <div className="content-title">{I18N.policyassets.tab.yeWuLeiXing}：</div>
                        <div className="content-text">
                            <Ellipsis title={businessType} widthLimit={180} />
                        </div>
                    </div>
                    <div className="basic-container">
                        <div className="content-title">{I18N.policyassets.tab.shiJianLeiXing}：</div>
                        <div className="content-text">
                            <Ellipsis title={eventType} widthLimit={180} />
                        </div>
                    </div>
                    <div className="basic-container">
                        <div className="content-title">{I18N.policyassets.tab.zhiXingMoShi}：</div>
                        <div className="content-text">
                            <Ellipsis title={policyMode} widthLimit={180} />
                        </div>
                    </div>
                </div>
            </div>
            {graphJson && (
                <div className="graph-content">
                    <div className="graph-title">{I18N.policyassets.tab.liuChengXiangQing}</div>
                    <div>
                        <MMEditor type="chart" graphData={graphJson} appCode={policyData?.appCode} orgCode={policyData?.orgCode} />
                    </div>
                </div>
            )}
        </>
    );
};

export default Assets;
