import I18N from '@/utils/I18N';
import { Spin, Table, Tabs, Tag, Ellipsis } from 'tntd';
import { connect } from 'dva';
import { useState, useEffect, useRef } from 'react';
import history from '@/utils/history';
import { workflowAPI } from '../../../../../../services';
import './index.less';
const { TabPane } = Tabs;
const AssetsList = (props) => {
    const { data, globalStore, componentSumList, cardKey, uuid } = props;
    const { appMap = {}, orgMap = {} } = globalStore;

    const [currentTab, setCurrentTab] = useState('1');
    const [tableData, setTableData] = useState([]);
    const [loading, setLoading] = useState(false);
    const defaultTab = useRef();

    useEffect(() => {
        setCurrentTab(cardKey);
    }, [cardKey]);

    //首页点击传入
    useEffect(() => {
        setTableData(data);
    }, [data]);

    const columnsMap = {
        RuleSetServiceNode: [
            {
                title: I18N.policyassets.tab.guiZeJiMingCheng,
                dataIndex: 'ruleSetName',
                width: '250px',
                key: 'ruleSetName',
                render: (text, record) => {
                    return (
                        <div className="ruleSetName-table-col-item">
                            <Tag color="#f4eff8" style={{ border: '1px solid #c4a7d8', color: '#9560b9' }}>
                                V{record?.version}
                            </Tag>
                            <span
                                style={{ width: '150px', display: 'inline-block', color: '#126bfb', cursor: 'pointer' }}
                                onClick={() => {
                                    history.push('/noah/ruleSet?currentTab=1&code=' + record.ruleSetCode);
                                }}>
                                <Ellipsis title={<span>{text} </span>} />
                            </span>
                            <br />
                            <span className="text-code" style={{ display: 'inline-flex' }}>
                                {I18N.policyassets.tab.biaoZhi}
                                <span style={{ width: '150px' }}>
                                    <Ellipsis title={<span>{record?.ruleSetCode} </span>} />
                                </span>
                            </span>
                        </div>
                    );
                }
            },
            {
                title: I18N.policyassets.tab.fengXianBiaoQian,
                dataIndex: 'riskTypeName',
                width: '150px',
                render: (text) => {
                    return (
                        <>
                            <span style={{ width: '150px', display: 'inline-block' }}>
                                <Ellipsis title={text} />
                            </span>
                            <br />
                        </>
                    );
                }
            },
            {
                title: I18N.policyassets.tab.moShi,
                dataIndex: 'exeMode',
                render: (text) => {
                    return (
                        <>
                            <span>{text}</span>
                            <br />
                        </>
                    );
                }
            },

            {
                title: I18N.policyassets.tab.suoShuJiGouQu,
                dataIndex: 'orgCode',
                width: 200,
                key: 'orgCode',
                render: (text, record) => {
                    return <Ellipsis title={orgMap[record?.orgCode]?.name + '/' + appMap[record?.appCode]} />;
                }
            },
            {
                title: I18N.policyassets.tab.miaoShu,
                dataIndex: 'desc',
                key: 'desc',
                render: (text) => {
                    return text ? (
                        <>
                            <span style={{ width: '150px', display: 'inline-block' }}>
                                <Ellipsis title={<span>{text}</span>} />
                            </span>
                            <br />
                        </>
                    ) : (
                        '- -'
                    );
                }
            }
        ],
        ModelServiceNode: [
            {
                title: I18N.policyassets.tab.moXingMingCheng,
                dataIndex: 'modelName',
                width: '450px',
                key: 'modelName',
                render: (text, record) => {
                    return (
                        <>
                            <Tag color="#f4eff8" style={{ border: '1px solid #c4a7d8', color: '#9560b9' }}>
                                {record?.version}
                            </Tag>
                            <span style={{ width: '150px', display: 'inline-block' }}>
                                <Ellipsis title={<span>{text}</span>} />
                            </span>
                            <br />
                            <span className="text-code" />
                        </>
                    );
                }
            },

            {
                title: I18N.policyassets.tab.moXingLeiXing,
                dataIndex: 'modelType',
                render: (text) => {
                    return (
                        <>
                            <span>{text}</span>
                            <br />
                        </>
                    );
                }
            }
        ],
        DecisionToolServiceNode: [
            {
                title: I18N.policyassets.tab.jueCeGongJuMing,
                dataIndex: 'decisionToolName',
                width: '350px',
                key: 'decisionToolName',
                render: (text, record) => {
                    return (
                        <div className="decisionToolName-table-col-item">
                            <Tag color="#f4eff8" style={{ border: '1px solid #c4a7d8', color: '#9560b9' }}>
                                V{record?.version}
                            </Tag>
                            <span
                                style={{ width: '150px', display: 'inline-block', color: '#126bfb', cursor: 'pointer' }}
                                onClick={() => {
                                    history.push('/noah/bodyguard/modelTool?code=' + record.decisionToolCode);
                                }}>
                                <Ellipsis title={<span>{text}</span>} />
                            </span>
                            <br />
                            <span className="text-code" style={{ display: 'inline-flex' }}>
                                {I18N.policyassets.tab.biaoZhi}
                                <Ellipsis title={<span>{record?.decisionToolCode}</span>} />
                                {/* {record?.decisionToolCode} */}
                            </span>
                        </div>
                    );
                }
            },

            {
                title: I18N.policyassets.tab.jueCeGongJuLei,
                dataIndex: 'decisionToolType',
                width: 200,
                render: (text) => {
                    return (
                        <>
                            <span>{text}</span>
                            <br />
                        </>
                    );
                }
            },
            {
                title: I18N.policyassets.tab.miaoShu,
                dataIndex: 'desc',
                key: 'desc',
                render: (text) => {
                    return text ? (
                        <>
                            <span style={{ width: '150px', display: 'inline-block' }}>
                                <Ellipsis title={<span>{text}</span>} />
                            </span>
                            <br />
                        </>
                    ) : (
                        '- -'
                    );
                }
            }
        ],
        FunctionServiceNode: [
            {
                title: I18N.policyassets.tab.hanShuMingCheng,
                dataIndex: 'functionName',
                width: '250px',
                key: 'functionName',
                render: (text, record) => {
                    return (
                        <div className="functionName-table-col-item">
                            <Tag color="#f4eff8" style={{ border: '1px solid #c4a7d8', color: '#9560b9' }}>
                                V{record?.version}
                            </Tag>
                            <span
                                style={{ width: '150px', display: 'inline-block', color: '#126bfb', cursor: 'pointer' }}
                                onClick={() => {
                                    history.push('/noah/formula?code=' + record.functionCode);
                                }}>
                                <Ellipsis title={<span>{text}</span>} />
                            </span>
                            <br />
                            <span className="text-code" style={{ display: 'inline-flex' }}>
                                {I18N.policyassets.tab.biaoZhi}
                                <Ellipsis title={<span> {record?.functionCode}</span>} />
                            </span>
                        </div>
                    );
                }
            },

            {
                title: I18N.policyassets.tab.hanShuLeiXing,
                dataIndex: 'functionType',
                render: (text) => {
                    return <Tag color={text === 1 ? 'geekblue' : 'cyan'}>{text}</Tag>;
                }
            },
            {
                title: I18N.policyassets.tab.miaoShu,
                dataIndex: 'desc',
                key: 'desc',
                render: (text) => {
                    return text ? (
                        <>
                            <span style={{ width: '150px', display: 'inline-block' }}>
                                <Ellipsis title={<span>{text}</span>} />
                            </span>
                            <br />
                        </>
                    ) : (
                        '- -'
                    );
                }
            }
        ],
        ScoreCardServiceNode: [
            {
                title: I18N.policyassets.tab.pingFenKaMingCheng,
                dataIndex: 'scoreCardName',
                width: '250px',
                key: 'scoreCardName',
                render: (text, record) => {
                    return (
                        <div className="scoreCardName-table-col-item">
                            <Tag color="#f4eff8" style={{ border: '1px solid #c4a7d8', color: '#9560b9' }}>
                                V{record?.version}
                            </Tag>
                            <span
                                style={{ width: '150px', display: 'inline-block', color: '#126bfb', cursor: 'pointer' }}
                                onClick={() => {
                                    history.push('/noah/bodyguard/scoreCard?code=' + record.scoreCardCode);
                                }}>
                                <Ellipsis title={<span>{text}</span>} />
                            </span>
                            <br />
                            <span className="text-code" style={{ display: 'inline-flex' }}>
                                {I18N.policyassets.tab.biaoZhi2}
                                <Ellipsis title={<span> {record?.scoreCardCode}</span>} />
                            </span>
                        </div>
                    );
                }
            },

            {
                title: I18N.policyassets.tab.pingFenKaLeiXing,
                dataIndex: 'scoreCardMode',
                render: (text) => {
                    return text;
                }
            },
            {
                title: I18N.policyassets.tab.miaoShu,
                dataIndex: 'desc',
                key: 'desc',
                render: (text) => {
                    return text ? (
                        <>
                            <span style={{ width: '150px', display: 'inline-block' }}>
                                <Ellipsis title={<span>{text}</span>} />
                            </span>
                            <br />
                        </>
                    ) : (
                        '- -'
                    );
                }
            }
        ],
        ThirdServiceNode: [
            {
                title: I18N.policyassets.tab.sanFangMingCheng,
                dataIndex: 'serviceName',
                width: '250px',
                key: 'serviceName',
                render: (text, record) => {
                    return (
                        <div className="serviceName-table-col-item">
                            <span
                                style={{ width: '150px', display: 'inline-block', color: '#126bfb', cursor: 'pointer' }}
                                onClick={() => {
                                    history.push('/handle/supplierManagement/dataServiceList?name=' + record.serviceCode);
                                }}>
                                <Ellipsis title={<span>{text}</span>} />
                            </span>
                            <br />
                            <span className="text-code">
                                {I18N.policyassets.tab.biaoZhi}
                                {record?.serviceCode}
                            </span>
                        </div>
                    );
                }
            }
            //本期暂不显示
            // {
            //     title: '缓存天数',
            //     dataIndex: 'cacheDay',
            //     render: (text) => {
            //         return (
            //             <>
            //                 <span>{text ? text : '--'}</span>
            //                 <br />
            //             </>
            //         );
            //     }
            // },
            // {
            //     title: '超时时间',
            //     dataIndex: 'timeout',
            //     render: (text) => {
            //         return (
            //             <>
            //                 <span>{text ? text + 'ms' : '--'}</span>
            //                 <br />
            //             </>
            //         );
            //     }
            // },
            // {
            //     title: '重试次数',
            //     dataIndex: 'retry',
            //     render: (text) => {
            //         return (
            //             <>
            //                 <span>{text ? text : '--'}</span>
            //                 <br />
            //             </>
            //         );
            //     }
            // }
        ],
        ChildFlowNode: [
            {
                title: I18N.policyassets.tab.ziCeLueMingCheng,
                dataIndex: 'policyName',
                width: '450px',
                key: 'policyName',
                render: (text, record) => {
                    return (
                        <>
                            <span style={{ width: '150px', display: 'inline-block' }}>
                                <Ellipsis
                                    title={
                                        <a
                                            onClick={() => {
                                                history.push('/noah/policyManage?currentTab=1&code=' + record.policyCode);
                                            }}>
                                            {text}
                                        </a>
                                    }
                                />
                            </span>
                        </>
                    );
                }
            },

            {
                title: I18N.policyassets.tab.ziCeLueBiaoZhi,
                dataIndex: 'policyCode',
                render: (text) => {
                    return (
                        <>
                            <span>{text}</span>
                            <br />
                        </>
                    );
                }
            }
        ],
        FeatureServiceNode: [
            {
                title: I18N.policyassets.tab.sanFangMingCheng,
                dataIndex: 'serviceName',
                // width: '250px',
                key: 'serviceName',
                render: (text, record) => {
                    return (
                        <>
                            {/* width: '150px', */}
                            <span style={{ display: 'inline-block', color: '#126bfb', cursor: 'pointer' }}>
                                <Ellipsis title={<span>{text}</span>} />
                            </span>
                            <br />
                            <span className="text-code">
                                {I18N.policyassets.tab.biaoZhi}
                                {record?.serviceCode}
                            </span>
                        </>
                    );
                }
            }
        ]
    };

    const getList = async () => {
        const key = defaultTab.current;
        const params = {
            uuid,
            componentType: key
        };
        setLoading(true);
        await workflowAPI
            .assetsComponent(params)
            .then((res) => {
                if (res?.success && key === defaultTab.current) {
                    setTableData(res?.data);
                }
            })
            .finally(() => {
                setLoading(false);
            });
    };

    return (
        <div className="assets-list">
            <div className="list-content">
                <Tabs
                    className="list-tabs"
                    activeKey={currentTab ? currentTab : '1'}
                    onChange={(key) => {
                        defaultTab.current = key;
                        getList();
                        setCurrentTab(key);
                    }}>
                    {componentSumList?.map((item) => {
                        return (
                            <TabPane tab={`${item?.componentTypeName}(${item.totalCount})`} key={item?.componentType}>
                                <Spin spinning={loading}>
                                    <Table
                                        rowKey={(row, index) => item.componentType + '_' + index}
                                        columns={columnsMap[item.componentType]}
                                        dataSource={tableData}
                                        pagination={false}
                                    />
                                </Spin>
                            </TabPane>
                        );
                    })}
                </Tabs>
            </div>
        </div>
    );
};

export default connect((state) => ({
    globalStore: state.global
}))(AssetsList);
