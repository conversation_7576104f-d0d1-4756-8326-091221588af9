.policy-assets {
    position: relative;
    padding: 16px 20px 16px;

    .center-content {
        color: white;
        width: 100%;
        background-color: #2373F4;
        height: 160px;
    }
    .title-content {
        text-align: center;
        .background-content {
            position: relative;
            width: 858px;
            height: 216px;
            background: url("@/sources/images/policy/bg_mid.png") no-repeat;
            background-size: contain;
            top: -2px;
        }
        .title-top {
            font-family: PingFangSC-Medium;
            font-size: 14px;
            color: #ffffff;
            letter-spacing: 0;
            font-weight: 400;
            display: flex;
            flex-direction: column;
            padding-top: 42px;
            .top-title {
                .first-content {
                    padding: 6px;
                    cursor: pointer;
                    width: 56px;
                }
                .second-content {
                    font-family: PingFangSC-Medium;
                    font-size: 24px;
                    a {
                        text-align: center;
                        color: #ffffff;
                        letter-spacing: 0;
                        font-weight: 500;
                    }
                    span {
                        display: inline-block;
                        padding: 0px 8px;
                        border: 1px solid rgba(255,255,255,0.7);
                        border-radius: 1px;
                        margin-right: 4px;
                        font-size: 16px;
                        color: #ffffff;
                        letter-spacing: 0;
                        font-weight: 400;
                        line-height: 22px;
                        vertical-align: 2px;
                    }
                }
                .generate-time{
                    opacity: 0.8;
                    font-family: ArialMT;
                    font-size: 14px;
                    line-height: 21px;
                    font-weight: 400;
                }
            }
            .bottom-title {
                display: flex;
                justify-content: center;
            }
        }
    }
    .bottom-content {

        .item-part {
            margin-top: 10px;
            display: inline-block;
            vertical-align: top;
            width: 200px;
            height: 80px;
            margin-right: 20px;
            background: #eaf4fe;
            border-radius: 2px;
            &:hover {
                cursor: pointer;
                .item-count,
                .item-type {
                    color: #126bfb;
                }
            }
            .item-count {
                color: #17233D;
                font-family: DINAlternate-Bold;
                font-size: 24px;

                letter-spacing: 0;
                font-weight: 700;
                padding: 10px 0px 0px 17px;
            }
            .item-type {
                margin-top: 10px;
                color: #8B919E;
                height: 17px;
                font-family: PingFangSC-Regular;
                font-size: 12px;
                letter-spacing: 0;
                font-weight: 400;
                margin-left: 17px;
            }
        }
    }

    .home-tabs {
        margin-top: -50px;
        .ant-tabs-tab {
            width: 60px;
            margin-right: 50px;
        }
        & > .ant-tabs-bar {
            color: white !important;
            border: none !important;
            margin-bottom: 23px;

            .ant-tabs-ink-bar {
                background-color: white;
                position: relative;
                left: 38px;
                top: -7px;
                width: 12px !important;
            }
        }
        .ant-tabs-tab-active {
            color: white;
        }
        .ant-tabs-tab:hover {
            color: white;
        }
        .ant-tabs-nav-scroll {
            width: 210px;
            margin: 0 auto;
        }
        .ant-tabs-nav-wrap {
            margin-bottom: -7px !important;
        }
    }
    .home-tabs-en {
        margin-top: -50px;
        .ant-tabs-tab {
            width: 60px;
            margin-right: 50px;
        }
        & > .ant-tabs-bar {
            color: white !important;
            border: none !important;
            margin-bottom: 23px;

            .ant-tabs-ink-bar {
                background-color: white;
                position: relative;
                left: 38px;
                top: -7px;
                width: 12px !important;
            }
        }
        .ant-tabs-tab-active {
            color: white;
        }
        .ant-tabs-tab:hover {
            color: white;
        }
        .ant-tabs-nav-scroll {
            width: 320px;
            margin: 0 auto;
        }
        .ant-tabs-nav-wrap {
            margin-bottom: -7px !important;
        }
    }

    .overview-content {
        padding: 20px;
        margin: 0 auto;
        border-radius: 4px;
        margin: 0 auto;
        position: relative;
        background-color: white;
        height: 700px;
        
        #tree {
            padding: 50px 0px 20px 20px;
            border: 1px solid rgba(225, 230, 238, 1);
            border-radius: 2px;
            width: 100%;
            height: 100%;
            overflow: scroll;
            height: 620px;
            .ant-spin-nested-loading.tree-spin{
                width: 100%;
            }
        }
        .overview-title {
            margin-bottom: 16px;
            font-family: PingFangSC-Medium;
            font-size: 14px;
            color: #17233d;
            letter-spacing: 0;
            font-weight: 500;

            &::before {
                margin-right: 6px;
                display: inline-block;
                content: "";
                width: 4px;
                height: 16px;
                background-color: #146cec;
                position: relative;
                top: 2.3px;
            }
        }
    }
    .basic-content {
        padding: 20px;
        border-radius: 4px;
        margin: 0 auto;
        margin-top: 12px;
        position: relative;
        background-color: white;
        .basic-title {
            font-family: PingFangSC-Medium;
            font-size: 14px;
            color: #17233d;
            letter-spacing: 0;
            font-weight: 500;
            margin-bottom: 16px;
            &::before {
                margin-right: 6px;
                display: inline-block;
                content: "";
                width: 4px;
                height: 16px;
                background-color: #146cec;
                position: relative;
                top: 2.3px;
            }
        }
        .basic-bottom {
            margin-bottom: -8px;
            .basic-container {
                display: inline-block;
                margin-bottom: 8px;
                width: 315px;
                .content-title {
                    display: inline-block;
                    // width: 80px;

                    height: 20px;
                    font-family: PingFangSC-Regular;
                    font-size: 14px;
                    color: #8b919e;
                    letter-spacing: 0;
                    font-weight: 400;
                    margin: 0px 20px 0px 0px ;
                }
                .content-text {
                    display: inline-block;
                    height: 20px;
                    font-family: PingFangSC-Regular;
                    font-size: 14px;
                    color: #17233d;
                    letter-spacing: 0;
                    font-weight: 400;
                }
            }
        }
    }
    .graph-content {
        padding: 20px;
        border-radius: 4px;
        margin: 0 auto;
        margin-top: 12px;
        position: relative;
        background-color: white;

        .graph-title {
            font-family: PingFangSC-Medium;
            font-size: 14px;
            color: #17233d;
            letter-spacing: 0;
            font-weight: 500;
            margin-bottom: 16px;
            &::before {
                margin-right: 6px;
                display: inline-block;
                content: "";
                width: 4px;
                height: 16px;
                background-color: #146cec;
                position: relative;
                top: 2.3px;
            }
        }
    }

    .overview-container {
        padding: 20px;
        background-color: white;
        margin: 0 auto;
        border-radius: 4px;
    }
    .overview-title {
        margin-bottom: 16px;
        font-family: PingFangSC-Medium;
        font-size: 14px;
        color: #17233d;
        letter-spacing: 0;
        font-weight: 500;
        &::before {
            margin-right: 6px;
            display: inline-block;
            content: "";
            width: 4px;
            height: 16px;
            background-color: #146cec;
            position: relative;
            top: 2.3px;
        }
    }
    .overview-toolbar {
        position: relative !important;
        left: 0px !important;
    }

    .job-editor{
        border: 1px solid rgba(225,230,238,1);
        padding: 3px;
        height: 400px;
    }
}
