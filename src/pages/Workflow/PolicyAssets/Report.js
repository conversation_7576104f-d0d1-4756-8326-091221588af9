import I18N, { getLang } from '@/utils/I18N';
import { Tabs } from 'tntd';
import moment from 'moment';
import { useState, useEffect } from 'react';
import { workflowAPI } from '../../../services';
import './index.less';
import Assets from './Tab/Assets';
import OverViewTab from './Tab/OverViewTab';

const { TabPane } = Tabs;
const PolicyAssets = (props) => {
    const { code, policyVersionUuid, version } = props;
    const [currentTab, setCurrentTab] = useState('1');
    const [data, setData] = useState();
    const [overviewData, setOverviewData] = useState([]);
    const [policyData, setPolicyData] = useState({});

    useEffect(() => {
        getAssetsOverviewData();
        getOverviewData();
        getPolicyName(policyVersionUuid);
    }, []);

    const getAssetsOverviewData = () => {
        let params = {
            uuid: policyVersionUuid
        };
        workflowAPI.getAssetsOverviewData(params).then((res) => {
            if (res?.success) {
                setData(res?.data);
            }
        });
    };

    const getOverviewData = () => {
        let params = {
            componentId: `${code}_${version}`,
            componentType: 'POLICY_VERSION',
            reference: true
        };
        workflowAPI.getOverviewData(params).then((res) => {
            if (res.success) {
                setOverviewData(res?.data);
            }
        });
    };

    const getPolicyName = (curPolicyVersionUuid) => {
        let params = {
            policyVersionUuid: curPolicyVersionUuid
        };
        // 详情信息
        workflowAPI.getDetail(params).then((res) => {
            if (res.success && res) {
                setPolicyData(res?.data);
            }
        });
    };

    return (
        <div className="policy-assets">
            <div className="center-content">
                <>
                    <img
                        src={require('../../../sources/images/policy/bg_left.svg')}
                        style={{ position: 'absolute', left: '40px', top: '22px' }}
                    />
                    <div className="title-content">
                        <div className="title-top">
                            <div className="top-title">
                                <div className="second-content">
                                    <span>V{policyData?.version}</span>
                                    <a>
                                        {policyData?.policyName}
                                        {I18N.policyassets.report.quanJingBaoGao}
                                    </a>
                                </div>

                                {/* 下载全景报告 */}
                                <div className="generate-time">
                                    {data?.reportDate &&
                                        I18N.template(I18N.policyassets.report.shengChengShiJianM, {
                                            val1: moment(data?.reportDate).format('YYYY-MM-DD HH:mm:ss')
                                        })}
                                </div>
                            </div>
                            <div className="bottom-title">
                                <div className="background-content" />
                            </div>
                        </div>
                    </div>
                    <img
                        src={require('../../../sources/images/policy/bg_right.svg')}
                        style={{ position: 'absolute', right: '40px', top: '22px' }}
                    />
                </>
            </div>
            <div className="bottom-content">
                <Tabs
                    activeKey={currentTab ? currentTab : '1'}
                    onChange={(key) => setCurrentTab(key)}
                    className={getLang() === 'en' ? 'home-tabs-en' : 'home-tabs'}
                    tabBarStyle={{
                        color: 'red !important',
                        border: 'none !important'
                    }}>
                    <TabPane tab={I18N.policyassets.report.ziChanQuanJing} key="1" className="home-tabs1">
                        <Assets
                            policyData={policyData}
                            data={data}
                            history={history}
                            code={code}
                            policyVersionUuid={policyVersionUuid}
                            version={version}
                        />
                    </TabPane>
                    <TabPane tab={I18N.policyassets.report.yinYongQuanJing} key="2">
                        <OverViewTab className="overView-tab" policyData={policyData} data={overviewData} history={history} />
                    </TabPane>
                </Tabs>
            </div>
        </div>
    );
};
export default PolicyAssets;
