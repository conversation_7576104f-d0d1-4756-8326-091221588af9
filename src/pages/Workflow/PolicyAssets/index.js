import I18N from '@/utils/I18N';
import { Breadcrumb, Icon } from 'tntd';
import { Route, Switch } from 'dva/router';
import TdBreadCrumb from '@tddc/bread-crumb';
import history from '@/utils/history';
import Report from './Report';
import List from './List';

const Entrance = (props) => {
    const { match = {} } = props;
    const { params = {} } = match;
    const { code, policyVersionUuid, version } = params;
    return (
        <Switch>
            <Route
                render={() => <List {...{ code, policyVersionUuid, version }} />}
                name={I18N.policyassets.index.lieBiao}
                path={`/noah/policyManage/assets/${code}/${policyVersionUuid}/${version}/list`}
            />
            <Route
                exact
                name={I18N.policyassets.index.quanJingBaoGao}
                render={() => <Report {...{ code, policyVersionUuid, version }} />}
                path={`/noah/policyManage/assets/${code}/${policyVersionUuid}/${version}`}
            />
            <Route name={I18N.policyassets.index.ceLueGuanLi} render={() => <Report {...{ code, policyVersionUuid, version }} />} path="/" />
        </Switch>
    );
};

export default TdBreadCrumb(Entrance, {
    BreadCrumbCustom: (breadList) => {
        const onlyTwoLevels = breadList?.length === 2;

        return (
            <Breadcrumb separator={!onlyTwoLevels ? '>' : ' '} className="c-breadcrumb">
                {breadList?.map((v, i) => {
                    if (onlyTwoLevels && i === 0) {
                        const dom = (
                            <>
                                <Icon type="left" className="go-back" />
                                {I18N.policyassets.index.fanHui}</>
                        );
                        return (
                            <Breadcrumb.Item key={v?.path}>
                                <a onClick={() => history.push('/noah/policyManage')}>{dom}</a>
                            </Breadcrumb.Item>
                        );
                    }
                    return (
                        <Breadcrumb.Item key={v?.path}>
                            {i === 0 ? v?.name : <a onClick={() => history.push(v?.path)}>{v?.name}</a>}
                        </Breadcrumb.Item>
                    );
                })}
            </Breadcrumb>
        );
    }
});
