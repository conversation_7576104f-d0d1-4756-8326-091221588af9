/*
 * @CreatDate: 2022-03-16 17:17:52
 * @Describe: 版本历史
 */

import I18N from '@/utils/I18N';
import { PureComponent } from 'react';
import { Modal, Table, Pagination } from 'tntd';
import { connect } from 'dva';
import moment from 'moment';
import PolicyTip from '@/components/PolicyTip';
import Ellipsis from '@/components/TablePage/components/Ellipsis';
import './index.less';
import { isJSON } from '@/utils/isJSON';

class VersionHistoryModal extends PureComponent {
    handleCancel = () => {
        this.props.onCancel();
    };

    render() {
        const { visible, changeHistoryPage, versionHistoryList } = this.props;
        const columns = [
            {
                title: I18N.workflowlist.modal.faBuHao,
                dataIndex: 'id',
                key: 'id',
                width: '150px',
                ellipsis: true,
                render: (text) => {
                    return <Ellipsis text={text} />;
                }
            },
            {
                title: I18N.workflowlist.modal.faBuRen,
                dataIndex: 'operator',
                key: 'operator',
                width: '150px',
                ellipsis: true,
                render: (text, record) => (
                    <>
                        <Ellipsis text={text} />
                    </>
                )
            },
            {
                title: I18N.workflowlist.modal.faBuShiJian,
                dataIndex: 'onlineDate',
                key: 'onlineDate',
                width: '200px',
                ellipsis: true,
                render: (text) => {
                    return <Ellipsis text={moment(text).format('YYYY-MM-DD HH:mm:ss')} />;
                }
            },
            {
                title: I18N.workflowlist.modal.faBuMoShi,
                dataIndex: 'type',
                key: 'type',
                width: '150px',
                ellipsis: true,
                render: (text, record) => (
                    <>
                        <Ellipsis text={text} />
                    </>
                )
            },
            {
                title: I18N.workflowlist.modal.faBuXinXi,
                dataIndex: 'config',
                key: 'config',
                render: (text, record, index) => {
                    return (
                        <>
                            {text && isJSON(record?.config) && JSON.parse(record?.config)?.type === 1 ? (
                                <Ellipsis
                                    text={I18N.template(I18N.workflowlist.modal.ceLueVJS, {
                                        val1: JSON.parse(text)?.ordinaryConfig?.version || '1'
                                    })}
                                />
                            ) : (
                                <PolicyTip
                                    policyUuid={record?.uuid}
                                    type={record?.type === I18N.workflowlist.modal.fenLiuMoShi ? 2 : 3}
                                    conf={record.config}
                                />
                            )}
                        </>
                    );
                }
            }
        ];
        return (
            <Modal
                width="900px"
                className="versionHistoryModal-modal"
                maskClosable={false}
                title={I18N.workflowlist.modal.liShiBanBen} // 历史版本
                visible={visible}
                onCancel={this.handleCancel}
                destroyOnClose
                footer={null}>
                <Table pagination={false} bordered columns={columns} dataSource={versionHistoryList?.contents} />
                <Pagination
                    current={versionHistoryList?.curPage}
                    defaultPageSize={10}
                    onChange={(current, pageize) => {
                        changeHistoryPage(current, pageize);
                    }}
                    total={versionHistoryList?.total}
                />
                <div className="totalData">{I18N.template(I18N.workflowlist.modal.gongVERS, { val1: versionHistoryList?.total || 0 })}</div>
            </Modal>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global
}))(VersionHistoryModal);
