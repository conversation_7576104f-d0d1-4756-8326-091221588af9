/*
 * @Author: liu<PERSON>
 * @CreatDate: 2019-08-26 17:17:52
 * @Describe: 策略版本下线
 */

import I18N from '@/utils/I18N';
import { PureComponent } from 'react';
import { Modal, message, Input } from 'tntd';
import { connect } from 'dva';
import { workflowAPI } from '@/services';
import otp from './otp';

const { TextArea } = Input;
class OfflineModal extends PureComponent {
    state = {
        remark: null,
        loading: false
    };

    handleCancel = () => {
        this.props.onCancel();
    };

    handleAfterClose = () => {
        this.setState({
            remark: null
        });
    };

    handleOk = () => {
        const { remark } = this.state;
        const { dispatch, uuid, globalStore } = this.props;
        const operUser = globalStore.currentUser.account; // 申请人

        const params = {
            reason: remark,
            uuid
        };
        this.setState({ loading: true });
        workflowAPI
            .applyOffline(params)
            .then((res) => {
                this.setState({ loading: false });
                if (!res) return;
                if (res && res.success) {
                    this.handleCancel();
                    message.success(res.message || I18N.workflowlist.modal.caoZuoChengGong); // 操作成功
                    const currentApp = this.props.globalStore.currentApp;
                    const appName = currentApp ? currentApp.name : null;
                    dispatch({
                        type: 'workflowList/getRunListData',
                        payload: {
                            appName
                        }
                    });
                } else {
                    //
                }
            })
            .catch(() => {
                this.setState({ loading: false });
            });
    };

    changeField(e) {
        this.setState({
            remark: e.target.value
        });
    }

    render() {
        const { remark, loading } = this.state;
        const { visible } = this.props;

        return (
            <Modal
                maskClosable={false}
                title={I18N.workflowlist.modal.ceLueBanBenXia} // 策略版本下线
                visible={visible}
                confirmLoading={loading}
                onCancel={this.handleCancel}
                afterClose={this.handleAfterClose}
                onOk={this.handleOk}>
                {/* 意见: */}
                {I18N.workflowlist.modal.yiJian}:
                <TextArea
                    style={{ resize: 'none', height: '160px', marginTop: '5px' }}
                    placeholder={I18N.workflowlist.modal.qingShuRuYiJian} // 请输入意见，200字以内
                    rows={4}
                    max={2000}
                    value={remark}
                    onChange={(e) => this.changeField(e)}
                    maxLength={2000}
                />
                <div
                    style={{
                        fontSize: '12px',
                        position: 'absolute',
                        right: '35px',
                        bottom: '50px'
                    }}>
                    {remark ? remark.length : 0}/<span>{2000}</span>
                </div>
            </Modal>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global
}))(OfflineModal);
