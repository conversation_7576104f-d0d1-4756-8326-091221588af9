import I18N, { getLang } from '@/utils/I18N';
import { useEffect, useState } from 'react';
import { TntdModal, message } from 'tntd';
import { connect } from 'dva';
import { cloneDeep } from 'lodash';
import { formatTreeData } from '@/utils/utils';
import history from '@/utils/history';

import Overview from '@/components/Overview';
import Node from '@/components/Overview/Node';
import NodeDetailModal from '@/pages/Reference/Modal/NodeDetail';
import runtimeJS from '../../../../../../public/noah-resource/overview_library.jst';
import service from './service';

import './index.less';

// 折叠树
const collapseTree = (d) => {
    if (d.children) {
        d._children = d.children;
        d._children.forEach(collapseTree);
        d.children = null;
    }
};

const overviewModal = (props) => {
    let { visible, onCancel, data = {}, workflowListStore } = props;
    let { originChildren } = workflowListStore;
    const [treeData, setTreeData] = useState({});
    let { rootName, children } = treeData;

    const lang = getLang();
    const [nodeData, setNodeData] = useState([]);
    const [nodeDetailVisibel, setNodeDetailVisibel] = useState(false);

    useEffect(() => {
        let formatData = formatTreeData(data);
        setTreeData(formatData);
    }, [data]);

    const onDownload = (name) => {
        let data = cloneDeep(treeData);
        const props = JSON.stringify({
            data
        });
        const html = new Blob(
            [
                `
        <html>
            <meta charset="utf-8">
            <div id="root"></div>
            <script>
            window.propsData = ${props};
            window.curLang = '${lang}';
            ${runtimeJS}
            </script>
            <script>
                window.langRender();
            </script>
        </html>
        `
            ],
            { type: 'text/html' }
        );
        const url = URL.createObjectURL(html);
        const eleLink = document.createElement('a');
        eleLink.download = `${name}.html`;
        eleLink.style.display = 'none';
        eleLink.href = url;
        // 触发点击
        document.body.appendChild(eleLink);
        eleLink.click();
        // 然后移除
        document.body.removeChild(eleLink);
    };

    // 请求节点抽屉详情
    const getNodeDetail = (params) => {
        service.getNodeDetail(params).then((res) => {
            if (res.success) {
                setNodeData(res.data || []);
                setNodeDetailVisibel(true);
            }
        });
    };

    let title = (
        <div>
            <span style={{ marginRight: 20 }}>{I18N.workflowlist.modal.quanJingShiTu}</span>
            <a
                style={{
                    float: 'right',
                    marginRight: 70
                }}
                onClick={() => onDownload(treeData.nodeName)}>
                {I18N.workflowlist.modal.daoChuHTM}
            </a>
        </div>
    );

    return (
        <>
            <TntdModal visible={visible} title={title} onCancel={onCancel} className="tntd-flow-modal">
                <Overview
                    data={treeData}
                    options={{
                        fixed: true,
                        initType: false,
                        nodeDom: ({ node, nodeToggle, id, constants, fixed }) => (
                            <Node
                                constants={constants}
                                node={node}
                                nodeToggle={nodeToggle}
                                id={id}
                                onClick={(d) => {
                                    let params = {
                                        componentId: d.componentId,
                                        componentType: d.componentType
                                    };
                                    getNodeDetail(params);
                                }}
                                onEyeClick={(data) => {
                                    let { goLink } = data;
                                    if (goLink) {
                                        history.push(goLink);
                                    } else {
                                        message.warning(I18N.workflowlist.modal.zanWuLianJieTiao);
                                    }
                                }}
                                fixed={fixed}
                            />
                        ),
                        customPosition: (node) => {
                            let { data, parent, x, y } = node;
                            let { isGroupItem } = data;
                            // 节点是实例节点有且只有一个，不用连接线
                            if (isGroupItem === false && parent?.children?.length === 1) {
                                y -= 24;
                            }
                            return [x, y];
                        }
                    }}
                />
            </TntdModal>
            <NodeDetailModal visible={nodeDetailVisibel} onCancel={() => setNodeDetailVisibel(false)} data={nodeData} />
        </>
    );
};

export default connect((state) => ({
    globalStore: state.global,
    workflowListStore: state.workflowList
}))(overviewModal);
