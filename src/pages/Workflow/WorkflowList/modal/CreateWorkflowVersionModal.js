/*
 * @Author: liu<PERSON>
 * @CreatDate: 2019-08-26 17:17:52
 * @Describe: 创建策略版本弹框组件
 */

import I18N from '@/utils/I18N';
import { PureComponent } from 'react';
import { Modal, message, Select } from 'tntd';
import { connect } from 'dva';
import { workflowAPI } from '@/services';

const Option = Select.Option;
class CreateWorkflowVersionModal extends PureComponent {
    state = {
        sourceId: '',
        loading: false
    };

    handleCancel = () => {
        this.props.onCancel();
    };

    handleAfterClose = () => {
        this.setState({
            sourceId: ''
        });
    };

    handleOk = () => {
        const { sourceId } = this.state;
        const { workflowCode, getPolicyVersionList, workflowListStore, record, policyUuid } = this.props;
        const { expandedRowKeys } = workflowListStore;

        const params = {
            policyUuid: expandedRowKeys[0],
            workflowCode
        };
        if (sourceId) {
            params.sourceVersionUuid = sourceId;
        }
        params.policyUuid = policyUuid;
        this.setState({ loading: true });
        workflowAPI
            .createWorkflowVersion(params)
            .then(async (res) => {
                this.setState({ loading: false });
                if (!res) return;
                if (res && res.success) {
                    this.handleCancel();
                    getPolicyVersionList(record.uuid);

                    // 刷新策略列表，上线的新增策略版本会变成待发布
                    const { dispatch, workflowListStore, globalStore } = this.props;
                    const currentApp = globalStore.currentApp;
                    const appName = currentApp ? currentApp.name : null;
                    const { curPage, pageSize } = workflowListStore.editSearchParams;
                    dispatch({
                        type: 'workflowList/getEditListData',
                        payload: {
                            appName,
                            curPage,
                            pageSize
                        }
                    });
                } else {
                    //
                }
            })
            .catch(() => {
                this.setState({ loading: false });
            });
    };

    changeField(e) {
        this.setState({
            sourceId: e
        });
    }

    render() {
        const { sourceId, loading } = this.state;
        const { visible, sourceVersionList } = this.props;

        return (
            <Modal
                maskClosable={false}
                title={I18N.workflowlist.modal.chuangJianCeLueBan}
                visible={visible}
                confirmLoading={loading}
                onCancel={this.handleCancel}
                afterClose={this.handleAfterClose}
                onOk={this.handleOk}>
                {/* 编辑来源版本 */}
                {I18N.workflowlist.modal.xuanZeLaiYuanBan}
                <Select
                    style={{ width: '200px', marginLeft: '10px' }}
                    showSearch
                    optionFilterProp="children"
                    dropdownMatchSelectWidth={false}
                    value={sourceId}
                    onChange={(e) => this.changeField(e)}>
                    <Option value="" key="-1">
                        {/* 无 */}
                        {I18N.workflowlist.modal.wu}
                    </Option>
                    {sourceVersionList &&
                        sourceVersionList.map((item, index) => {
                            return (
                                <Option value={item.uuid} key={index}>
                                    V{item.version}
                                </Option>
                            );
                        })}
                </Select>
            </Modal>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global,
    workflowListStore: state.workflowList
}))(CreateWorkflowVersionModal);
