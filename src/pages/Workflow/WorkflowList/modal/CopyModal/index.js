/*
 * @CreatDate: 2019-08-23 11:21:49
 * @Describe: 策略创建、修改、查看
 */

import I18N from '@/utils/I18N';
import { useEffect, useState } from 'react';
import { connect } from 'dva';
import { Modal, Tooltip, Input, Select, Button, Form, TreeSelect, Checkbox, Radio } from 'tntd';
import { userAPI, workflowAPI } from '@/services';
import { traverseTree } from '@/utils/utils';
import otp from '@/pages/Workflow/otp';

const { TextArea } = Input;
const Option = Select.Option;

const CopyModal = (props) => {
    const { visible, globalStore, record, form, onCancel } = props;
    const { currentOrgCode, currentApp, orgList, allMap } = globalStore;
    const { eventType = [] } = allMap;
    const { getFieldDecorator, setFieldsValue, resetFields, getFieldValue } = form;

    const [loading, setLoading] = useState(false);
    const [appLists, setAppLists] = useState([]);
    const [versionList, setVersionList] = useState([]);
    const [policyList, setPolicyList] = useState([]);

    useEffect(() => {
        if (visible) {
            let findItem;
            traverseTree(orgList, (node) => {
                if (node.code === currentOrgCode) {
                    findItem = node;
                    return false;
                }
            });
            userAPI.getAppByOrgId({ orgUuid: findItem.uuid }).then((res) => {
                setAppLists(res?.data);
            });
            getPolicyList();
            policySelect();
        }
    }, [visible]);
    //获取当前策略下的版本列表
    const getPolicyList = () => {
        let params = {
            policyUuid: record?.uuid
        };
        workflowAPI.getPolicyList(params).then((res) => {
            if (res?.success) {
                setVersionList(res?.data);
            }
        });
    };
    //获取当前策略版本的可复制到的 新策略 的列表
    const policySelect = () => {
        let params = {
            policyMode: record?.mode,
            businessType: record?.businessType,
            policyScenario: record?.policyScenario || 1,
            childrenFlow: record?.childrenFlow
        };
        workflowAPI.policySelect(params).then((res) => {
            if (res?.success) {
                setPolicyList(res?.data);
            }
        });
    };
    const handleAfterClose = () => {
        resetFields();
    };

    const handleCancel = () => {
        onCancel();
    };

    const handleOk = () => {
        let { record, form } = props;
        form.validateFields(async (errors, data) => {
            if (!errors) {
                let { copyList = [], copyPolicy, copyMode } = data;
                if (copyMode === 1) {
                    let params = {
                        version: copyList.join(','),
                        code: copyPolicy,
                        sourceCode: record.code
                    };
                    workflowAPI.policyCopyOld(params).then((res) => {
                        if (res?.success) {
                            onCancel();
                        }
                    });
                } else {
                    let { name, code, appCode, orgCode, businessType, mode, description, eventType, scenario, childrenFlow } = data;
                    const params = {
                        //复制参数
                        version: copyList.join(','),
                        // code: copyPolicy,
                        sourceCode: record.code,
                        //新增参数
                        name,
                        code,
                        appCode,
                        orgCode,
                        eventType,
                        businessType,
                        mode,
                        description,
                        scenario
                    };
                    if (childrenFlow) {
                        params.childrenFlow = !!childrenFlow ? 1 : 0;
                    }
                    form.validateFields(async (errors, data) => {
                        if (!errors) {
                            workflowAPI.policyCopyNew(params).then((res) => {
                                if (res?.success) {
                                    onCancel();
                                }
                            });
                        }
                    });
                }
            }
        });
    };

    const formItemLayout = {
        labelCol: { span: 7 },
        wrapperCol: { span: 14 }
    };
    const getAppLists = (e) => {
        let findItem;
        traverseTree(orgList, (node) => {
            if (node.code === e) {
                findItem = node;
                return false;
            }
        });
        userAPI.getAppByOrgId({ orgUuid: findItem.uuid }).then((res) => {
            setAppLists(res?.data);
        });
    };
    return (
        // 联调
        <Modal
            width={otp.copyModalWidth}
            maskClosable={false}
            title={I18N.workflowlist.modal.fuZhiCeLue}
            className="m-creat-copy-modal"
            visible={visible}
            onCancel={handleCancel}
            afterClose={handleAfterClose}
            footer={[
                <Button key="cancel" onClick={handleCancel}>
                    {I18N.workflowlist.modal.quXiao}
                </Button>,
                <Button key="ok" type="primary" onClick={handleOk} loading={loading}>
                    {I18N.workflowlist.modal.queDing}
                </Button>
            ]}
            destroyOnClose>
            <Form {...formItemLayout}>
                <Form.Item label={I18N.workflowlist.modal.xuanZeXuFuZhi}>
                    {getFieldDecorator('copyList', {
                        rules: [
                            {
                                required: true,
                                message: I18N.workflowlist.modal.qingXuanZeXuFu
                            }
                        ]
                    })(
                        <Select style={{ width: '100%' }} mode="multiple" placeholder={I18N.workflowlist.modal.qingXuanZeXuFu}>
                            {versionList?.map((item) => {
                                return (
                                    <Option key={item.version} value={item.version}>
                                        V{item.version}
                                    </Option>
                                );
                            })}
                        </Select>
                    )}
                </Form.Item>
                <Form.Item label={I18N.workflowlist.modal.fuZhiMoShi}>
                    {getFieldDecorator('copyMode', {
                        initialValue: 1,
                        rules: [
                            {
                                required: true,
                                message: I18N.workflowlist.modal.qingXuanZeFuZhi2
                            }
                        ]
                    })(
                        <Radio.Group>
                            {window.auth('strategyManage', 'CopyOld') && <Radio value={1}>{I18N.workflowlist.modal.fuZhiDaoYiYou}</Radio>}
                            {window.auth('strategyManage', 'CopyNew') && <Radio value={2}>{I18N.workflowlist.modal.fuZhiXinCeLue}</Radio>}
                        </Radio.Group>
                    )}
                </Form.Item>
                {getFieldValue('copyMode') === 1 ? (
                    <Form.Item label={I18N.workflowlist.modal.fuZhiDaoDeCe}>
                        {getFieldDecorator('copyPolicy', {
                            rules: [
                                {
                                    required: true,
                                    message: I18N.workflowlist.modal.qingXuanZeFuZhi
                                }
                            ]
                        })(
                            <Select
                                placeholder={I18N.workflowlist.modal.qingXuanZeFuZhi}
                                showSearch
                                dropdownMatchSelectWidth={false}
                                filterOption={(inputValue, option) => {
                                    let label = option?.props?.children?.props?.title;
                                    if (typeof label === 'string') {
                                        label = label?.toLocaleLowerCase();
                                    }
                                    return label?.includes(inputValue?.toLocaleLowerCase());
                                }}
                                dropdownStyle={{
                                    maxWidth: '500px'
                                }}>
                                {policyList?.map((item) => {
                                    return (
                                        <Option title={item.policyName} key={item.policyCode} value={item.policyCode}>
                                            <Tooltip title={item.policyName} placement="topLeft">
                                                {item.policyName}
                                            </Tooltip>
                                        </Option>
                                    );
                                })}
                            </Select>
                        )}
                    </Form.Item>
                ) : (
                    <>
                        <Form.Item label={I18N.workflowlist.modal.suoShuJiGou}>
                            {getFieldDecorator('orgCode', {
                                initialValue: currentOrgCode,
                                rules: [
                                    {
                                        required: true,
                                        message: I18N.workflowlist.modal.qingTianXieJiGou
                                    }
                                ]
                            })(
                                <TreeSelect
                                    placeholder={I18N.workflowlist.modal.xuanZeJiGou}
                                    searchPlaceholder={I18N.workflowlist.modal.jiGouMingCheng}
                                    treeNodeFilterProp="title"
                                    showSearch
                                    treeData={orgList}
                                    treeDefaultExpandAll
                                    style={{ width: '100%', height: 28, lineHeight: 28 }}
                                    dropdownStyle={{
                                        maxHeight: 300,
                                        overflow: 'auto',
                                        width: 340
                                    }}
                                    onChange={(e) => {
                                        setFieldsValue({ appCode: undefined });
                                        getAppLists(e);
                                    }}
                                />
                            )}
                        </Form.Item>
                        <Form.Item label={I18N.workflowlist.modal.suoShuQuDao}>
                            {getFieldDecorator('appCode', {
                                rules: [
                                    {
                                        required: true,
                                        message: I18N.workflowlist.modal.qingTianXieQuDao
                                    }
                                ]
                            })(
                                <Select
                                    placeholder={I18N.workflowlist.modal.qingXuanZeQuDao}
                                    showSearch
                                    optionFilterProp="children"
                                    dropdownMatchSelectWidth={false}>
                                    {appLists &&
                                        appLists
                                            .filter((item) => item.have)
                                            .map((item) => {
                                                return (
                                                    <Option key={item.name} value={item.name}>
                                                        {item.displayName}
                                                    </Option>
                                                );
                                            })}
                                </Select>
                            )}
                        </Form.Item>
                        <Form.Item label={I18N.workflowlist.modal.ceLueMingCheng}>
                            {getFieldDecorator('name', {
                                rules: [
                                    {
                                        required: true,
                                        message: I18N.workflowlist.modal.qingTianXieCeLue3
                                    },
                                    {
                                        // pattern: /^[a-zA-Z0-9_\u4e00-\u9fa5]+$/,
                                        pattern: /^[a-zA-Z0-9_\u4e00-\u9fa5-.\s]+$/,
                                        message: I18N.workflowlist.modal.mingChengBiXuYou
                                    }
                                ]
                            })(<Input placeholder={I18N.workflowlist.modal.jinZhiChiZhongYing} />)}
                        </Form.Item>
                        <Form.Item label={I18N.workflowlist.modal.ceLueBiaoZhi}>
                            {getFieldDecorator('code', {
                                rules: [
                                    {
                                        required: true,
                                        message: I18N.workflowlist.modal.qingTianXieCeLue2
                                    }
                                ]
                            })(<Input placeholder={I18N.workflowlist.modal.qingTianXieCeLue2} />)}
                        </Form.Item>

                        <Form.Item label={I18N.workflowlist.modal.yeWuLeiXing}>
                            {getFieldDecorator('businessType', {
                                initialValue: record?.businessType,
                                rules: [
                                    {
                                        required: true,
                                        message: I18N.workflowlist.modal.qingTianXieYeWu
                                    }
                                ]
                            })(
                                <Select
                                    disabled={true}
                                    placeholder={I18N.workflowlist.modal.qingXuanZeYeWu}
                                    showSearch
                                    optionFilterProp="children"
                                    dropdownMatchSelectWidth={false}>
                                    {props.workflowListStore?.selectMap?.policyBusinessType
                                        ? Object.keys(props.workflowListStore.selectMap.policyBusinessType).map((key) => {
                                              return (
                                                  <Option key={parseInt(key, 10)} value={parseInt(key, 10)}>
                                                      {props.workflowListStore.selectMap.policyBusinessType[key]}
                                                  </Option>
                                              );
                                          })
                                        : null}
                                </Select>
                            )}
                        </Form.Item>
                        <Form.Item label={I18N.workflowlist.modal.ceLueZhiXingMo}>
                            {getFieldDecorator('mode', {
                                initialValue: record?.mode,
                                rules: [
                                    {
                                        required: true,
                                        message: I18N.workflowlist.modal.qingTianXieCeLue
                                    }
                                ]
                            })(
                                <Select
                                    disabled={true}
                                    placeholder={I18N.workflowlist.modal.qingXuanZeCeLue}
                                    showSearch
                                    optionFilterProp="children"
                                    dropdownMatchSelectWidth={false}>
                                    {props.workflowListStore?.selectMap?.policyModeType
                                        ? Object.keys(props.workflowListStore.selectMap.policyModeType).map((key) => {
                                              // eslint-disable-next-line radix
                                              if (key !== '0') {
                                                  return (
                                                      <Option value={parseInt(key, 10)}>
                                                          {props.workflowListStore.selectMap.policyModeType[key]}
                                                      </Option>
                                                  );
                                              }
                                          })
                                        : null}
                                </Select>
                            )}
                        </Form.Item>
                        {!!record?.childrenFlow && (
                            <Form.Item label={I18N.workflowlist.modal.ziCeLue}>
                                {getFieldDecorator('childrenFlow', {
                                    initialValue: record?.childrenFlow
                                })(
                                    <Checkbox disabled checked={!!record?.childrenFlow}>
                                        {I18N.workflowlist.modal.shi}
                                    </Checkbox>
                                )}
                            </Form.Item>
                        )}
                        {!record?.childrenFlow && (
                            <Form.Item label={I18N.workflowlist.modal.shiJianLeiXing}>
                                {getFieldDecorator('eventType', {
                                    rules: [
                                        {
                                            required: true,
                                            message: I18N.workflowlist.modal.qingXuanZeShiJian
                                        }
                                    ]
                                })(
                                    <Select
                                        placeholder={I18N.workflowlist.modal.qingTianXieShiJian}
                                        showSearch
                                        optionFilterProp="children"
                                        dropdownMatchSelectWidth={false}>
                                        {eventType?.map((item) => {
                                            return (
                                                <Option key={item.name} value={item.name}>
                                                    {item.dName}
                                                </Option>
                                            );
                                        })}
                                    </Select>
                                )}
                            </Form.Item>
                        )}

                        <Form.Item label={I18N.workflowlist.modal.miaoShu}>
                            {getFieldDecorator('description', {
                                rules: [
                                    {
                                        // required: true,
                                        // message: '请填写描述'
                                    }
                                ]
                            })(<TextArea placeholder={I18N.workflowlist.modal.qingTianXieMiaoShu} />)}
                        </Form.Item>
                    </>
                )}
            </Form>
        </Modal>
    );
};

export default Form.create()(
    connect((state) => {
        return {
            globalStore: state.global,
            workflowListStore: state.workflowList
        };
    })(CopyModal)
);
