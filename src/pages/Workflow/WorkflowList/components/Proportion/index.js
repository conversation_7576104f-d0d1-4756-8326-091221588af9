/*
 * @CreatDate: 2019-08-15 18:42:19
 * @Describe: 比例组件
 */

import I18N from '@/utils/I18N';
import { PureComponent } from 'react';
import { connect } from 'dva';
import { Select, InputNumber, Row, Col, Icon } from 'tntd';
import './index.less';
import { isEqual } from 'lodash';

const { Option } = Select;

class Proportion extends PureComponent {
    state = {
        versionList: []
    };

    componentDidMount() {
        const { versionList, data } = this.props;
        this.setState({
            versionList
        });
    }

    componentDidUpdate(preProps) {
        const preData = preProps.data;
        const nextData = this.props.data;
        if (!isEqual(preData, nextData)) {
            this.focusHandle();
        }
    }

    focusHandle = () => {
        const { versionList, data, mainVersion } = this.props;
        let copyVersionList = Object.assign([], versionList);
        copyVersionList.forEach((item) => {
            item.disabled = false;
        });
        data.forEach((item) => {
            copyVersionList.forEach((sItem) => {
                if (item.version === sItem.version || sItem.version === mainVersion) {
                    sItem.disabled = true;
                }
            });
        });

        this.setState({
            versionList: copyVersionList
        });
    };

    changeField(e, type, index) {
        this.props.onChangeField(e, type, index);
    }

    render() {
        const { versionList } = this.state;
        const { data, workflowType } = this.props;

        return (
            <div className="m-proportion">
                {data &&
                    data.length > 0 &&
                    data.map((item, index) => {
                        return (
                            <Row gutter={8} key={index}>
                                <Col className="gutter-row" span="8">
                                    <Select
                                        style={{ width: '100%' }}
                                        placeholder={I18N.workflowlist.components.qingXuanZeBanBen} // 请选择版本
                                        value={item.version ? item.version : undefined}
                                        onChange={(e) => this.changeField(e, 'version', index)}
                                        onDropdownVisibleChange={(open) => {
                                            if (open) this.focusHandle();
                                        }}>
                                        {versionList &&
                                            versionList.map((item, index) => {
                                                return (
                                                    <Option value={item.version} key={index} disabled={item.disabled}>
                                                        V{item.version}
                                                    </Option>
                                                );
                                            })}
                                    </Select>
                                </Col>
                                <Col className="gutter-row" span="8">
                                    <InputNumber
                                        style={{ width: '100%' }}
                                        placeholder={I18N.workflowlist.components.qingShuRuBaiFen} // 请输入百分比
                                        // min={1}
                                        // max={100}
                                        // formatter={value => value && parseFloat(value).toFixed(1)}
                                        step={0.01}
                                        precision={2}
                                        disabled={workflowType === '3'}
                                        value={item.ratio}
                                        onChange={(e) => this.changeField(e, 'ratio', index)}
                                    />
                                </Col>
                                <Col className="gutter-row" span="8">
                                    %
                                    {data.length !== 1 && (
                                        <Icon type="delete" className="u-delete" onClick={() => this.props.onDelete(index)} />
                                    )}
                                    {data.length === index + 1 && workflowType !== '3' && (
                                        <Icon type="plus-circle" className="u-add" onClick={() => this.props.onAdd()} />
                                    )}
                                </Col>
                            </Row>
                        );
                    })}
            </div>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global
}))(Proportion);
