.workflow-page{
    .expand-table {
      .ant-table-tbody{
        td{
            padding: 0px 0px 0px 26px!important;
        }
      }
    }
}

.m-expanded-table {
    .ant-table-column-title{
        white-space:break-spaces !important;
    }
    .expand-table {
        border: none;
        height: 0px;
        overflow: hidden;
        // transition: height 0.3s;
        // -webkit-transition: height 0.3s; /* Safari */
        max-width: 97%;
        th{
            padding: 9px 28px !important;
            background: white !important;
        }
        td{
            border-bottom: inherit !important;
        }
        .ant-table-column-title{

            font-family: PingFangSC-Medium;
            font-size: 14px;
            color: #454F64;
            letter-spacing: 0;
            font-weight: 500;
        }
    }

    .triangle-click {
        // position: relative;
        // left: 29px;

        display: block;

        // td{
        //     padding: 0px 0px 0px 27px!important;
        // }
    }

    @keyframes rotate {
        0% {
            transform: rotate(270deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }
    @keyframes rotate1 {
        0% {
            transform: rotate(360deg);
        }
        100% {
            transform: rotate(270deg);
        }
    }

    .triangle-rotate-reserve {
        animation-fill-mode: forwards;
        animation-name: rotate1;
        animation-duration: 0.3s;
    }
    .triangle-rotate {
        animation-fill-mode: forwards;
        animation-name: rotate;
        animation-duration: 0.3s;
    }

    .triangle {
        transform: rotate(270deg);
        cursor: pointer;
        display: inline-block;
        width: 0px;
        height: 0px;
        border-top: 7px solid;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        position: relative;
        left: 11px;
        top: -2px;
    }

    .anticon-redo {
        font-size: 16px;
        height: 20px;
        width: 16px;
        position: relative;
        top: 2px;
        color: #455063;
    }
    .policy-info {
        width: calc(100% - 56px);
    }
    .first-td {
        &.first-td-child{
            .tnt-ellipsis {
                // max-width: 210px;
                max-width: calc(80% - 50px);
            }
        }
        .tnt-ellipsis {
            // max-width: 210px;
            max-width: 80%;
        }
        .child-flow-tag{
            height:20px;
            line-height: 18px;
            margin-left: 6px;
            margin-right:0;
        }
        .offline-tag{
            height:20px;
            line-height: 18px;
            margin-left: 6px;
            margin-right:0;
        }
    }
    .table-actions {
        display: flex;
        align-items: center;
    }
    .a-disabled {
        // opacity: 0.5;
    }
    .anticon-sketch {
        svg {
            top: 4px;
            font-size: 20px;
            color: #455064;
            position: relative;
        }
    }
    .hidden-divider {
        visibility: hidden;
    }
    .ant-steps-icon {
        top: 0px;
    }
    // .ant-steps-dot {
    //     height: 42px !important;
    // }
    .white-divider {
        height: 15px;
        background: #e1e6ee;
    }
    .blue-divider {
        height: 15px;
        background: #126bfb;
        margin: 0px 9px 0px 9px !important;
    }
    .ant-steps-item-icon {
        margin-top: 15px !important;
        height: 11px;
    }
    .ant-steps-dot {
        padding-bottom: 2px !important;
    }
    .ant-table-expanded-row td {
        border-bottom: 1px solid #ebeef5;
        border-bottom-width: 1px;
        border-bottom-style: solid;
        border-bottom-color: rgb(235, 238, 245);
        transition: all 0.3s, border 0s;
        transition-property: all, border;
        transition-duration: 0.3s, 0s;
        transition-timing-function: ease, ease;
        transition-delay: 0s, 0s;
        // padding: 4px 0px !important;
    }
    .ant-table-scroll {
        overflow: visible;
    }
    tr.ant-table-expanded-row,
    tr.ant-table-expanded-row:hover {
        background: white !important;
    }
    .ant-table-wrapper {
        margin: 0px 0px 0px !important;
        // border-left: 1px solid #e1e6ee;
        box-shadow: none !important;
    }
    .ant-steps-icon-dot {
        background: #5e7092 !important;
        left: -8px !important;
        width: 8px !important;
        height: 8px !important;
        position: absolute !important;
    }
    .ant-steps-item-content {
        display: inline !important;
    }
    .ant-steps-item-description {
        overflow-x: auto !important
        // width: 1100px !important;
    }
    .ant-steps-item-content {
        // width: 1100px !important;
    }
    .ant-steps-item-description {
        padding-bottom: 0px !important;
    }
    .description-content {
        display: inline-block;
        padding: 9px 0px 9px 0px;
        background-color: rgba(225, 230, 238, 0.3);
        border-radius: 2px;
        // width: ~"calc(100vw - 425px)";
        min-width: 97%;
        // top: 3px;

        position: relative;
        .description-version {
            margin-left: 22px;
        }
        .version-value {
            margin-right: 30px;
            height: 20px;
            font-family: PingFangSC-Regular;
            font-size: 14px;
            letter-spacing: 0;
            line-height: 20px;
            font-weight: 400;
            width: 140px;
            display: inline-block;
            padding-left: 20px;
            > span {
                color: #126bfb;
            }
        }
    }

    .ant-steps {
        // display: flex !important;
        // align-items: center;
    }

    .ant-table-expanded-row {
        td {
            td {
                border-bottom: none;
            }
        }
    }
    tbody tr {
        height: 40px;
    }
    .table-row {
        margin: -16px -8px -16px -58px;
        .row {
            border-bottom: 1px solid #ddd;
            display: flex;
            padding: 9px 0 9px 58px;
            &:last-child {
                border-bottom: 0;
            }
            .row-item {
                padding-left: 24px;
                line-height: 30px;
            }
            .u-checkbox {
                position: absolute;
                left: -48px;
                top: 0;
            }
            a {
                color: #ff9800;
            }
        }
        .none-data {
            text-align: center;
            padding: 20px 0;
            i {
                font-size: 24px;
            }
        }
    }
    .u-dot {
        display: inline-block;
        width: 8px;
        height: 8px;
        background: #ccc;
        border-radius: 50%;
        vertical-align: middle;
        margin-right: 3px;
    }
    .ant-divider,
    .ant-divider-vertical {
        margin: 0 6px;
    }
    .ant-table-row-collapsed,
    .ant-table-row-expanded {
        border-radius: 2px;
        color: #fff;
        font-weight: bold;
    }
    .ant-table-row-collapsed {
        background: #86d16b;
        border: 1px solid #86d16b;
    }
    .ant-table-row-expanded {
        border: 1px solid #f17063;
        background: #f17063;
    }
}
.ant-dropdown .u-more-operate li.ant-dropdown-menu-item a {
    text-align: center;
    color: #1890ff;
}
.ant-dropdown .u-more-operate2 li.ant-dropdown-menu-item a {
    text-align: center;
    color: #ff9800;
}
// NestedTable样式更改
// 内表格
.tntx-table-expand-fixed-inner {
    // 背景色
    tr.ant-table-row.ant-table-row-level-0 {
        background: white;
    }
    // hover色去除
    .ant-table-thead > tr.ant-table-row-hover:not(.ant-table-expanded-row) > td,
    .ant-table-tbody > tr.ant-table-row-hover:not(.ant-table-expanded-row) > td,
    .ant-table-thead > tr:hover:not(.ant-table-expanded-row) > td,
    .ant-table-tbody > tr:hover:not(.ant-table-expanded-row) > td {
        background: white !important; //Change the existing color to `unset`
    }
    // 内部右侧按钮
    .ant-table-fixed-right {
        td a {
            color: #ff9800;
        }
    }
}

.mode-style {
    width: 38px;
    height: 38px;
    border-radius: 2px;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin-right: 6px;
    &.mode-flow {
        background-image: url(../../../../../sources/images/policy/icon-flow.svg);
    }
    &.mode-simple {
        background-image: url(../../../../../sources/images/policy/icon-simple.svg);
    }
}
