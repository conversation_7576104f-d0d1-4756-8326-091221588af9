import I18N from '@/utils/I18N';
import { Children, useEffect, useState } from 'react';
import moment from 'moment';
import { isArray } from 'lodash';
import domtoimage from '@/utils/dom-to-image';
import MMEditor from '@/components/workflow/MMEditor';
import { workflowAPI } from '../../../../../services';

import DecisionTree from './DecisionTree';

import './index.less';
let editor;
const AssetsExport = (props) => {
    //record 父表格（策略）
    //policyRecord 子表格（策略版本）
    //data 决策树数据
    const { data, record, curRecord, onCancel } = props;
    const [graphJson, setGraphJson] = useState();

    // const [src, setSrc] = useState();
    // 转svg
    // const dataURLtoFile = (dataUrl, fileName) => {
    //     var arr = dataUrl.split('data:image/svg+xml;charset=utf-8,')[1];
    //     let base64 = window.btoa(unescape(encodeURIComponent(arr)));
    //     base64.replace(/\n/g, '<br>');
    //     setSrc(base64);
    //     return base64;
    // };

    useEffect(() => {
        if (curRecord?.uuid && isArray(data)) {
            //等待加载完决策树
            setTimeout(() => {
                let dtreeGraph = [];
                data?.map((item) => {
                    let node = document.querySelector(`.${item.decisionToolCode} .graph-container`);
                    if (node) {
                        domtoimage
                            .toPng(node, { scale: 1 })
                            .then((dataUrl) => {
                                let obj = {
                                    decisionToolCode: item.decisionToolCode,
                                    content: dataUrl
                                };
                                dtreeGraph.push(obj);
                                // let link = document.createElement('a');
                                // link.setAttribute('href', dataUrl);
                                // link.setAttribute('download', 1 + '.png');
                                // document.body.appendChild(link);
                                // link.click();
                            })
                            .finally(() => {
                                // setDtreeGraph(arr);
                            });
                    }
                });

                let params = {
                    policyVersionUuid: curRecord.uuid,
                    areaType: 2
                };
                workflowAPI.getDetail(params).then(async (res) => {
                    if (res?.success) {
                        let nowDate = moment(new Date().getTime()).format('YYYYMMDD');
                        if (res?.data.graphJson) {
                            //流模式
                            setGraphJson(res?.data.graphJson);
                            let node = document.getElementsByClassName('downloadFile-chart')[0];
                            let { width, height } = editor.paper.getBBox();
                            node.style.height = height + 200 + 'px';
                            node.style.width = width + 200 + 'px';
                            await setTimeout(() => {
                                domtoimage
                                    .toPng(node, { scale: 3 })
                                    .then(function (dataUrl) {
                                        // node.style.position = 'absolute';
                                        // node.style.zIndex = '-11';
                                        let params = {
                                            uuid: curRecord.policyUuid,
                                            policyCode: record.code,
                                            policyVersion: curRecord.version,
                                            // policyGraph: dataURLtoFile(dataUrl),
                                            policyGraph: dataUrl,
                                            type: 'excel',
                                            fileType: 'xls',
                                            fileName: I18N.template(I18N.workflowlist.components.rECOR, {
                                                val1: record.name,
                                                val2: curRecord.version,
                                                val3: nowDate
                                            })
                                        };
                                        if (dtreeGraph.length > 0) {
                                            params.dtreeGraph = dtreeGraph;
                                        }
                                        // let link = document.createElement('a');
                                        // link.setAttribute('href', dataUrl);
                                        // link.setAttribute('download', 1 + '.png');
                                        // document.body.appendChild(link);
                                        // link.click();
                                        workflowAPI.assetsExport(params);
                                    })
                                    .finally(() => {
                                        // node && node.remove();
                                        onCancel();
                                        setGraphJson();
                                    });
                            }, 500);
                        } else {
                            //简易模式
                            let params = {
                                uuid: curRecord.policyUuid,
                                policyCode: record.code,
                                policyVersion: curRecord.version,
                                type: 'excel',
                                fileType: 'xls',
                                fileName: I18N.template(I18N.workflowlist.components.rECOR, {
                                    val1: record.name,
                                    val2: curRecord.version,
                                    val3: nowDate
                                })
                            };
                            if (dtreeGraph.length > 0) {
                                params.dtreeGraph = dtreeGraph;
                            }
                            workflowAPI.assetsExport(params).finally(() => {
                                onCancel();
                            });
                        }
                    }
                });
            }, 1000);
        }
    }, [curRecord, data]);

    return (
        <div className="assets-export">
            {graphJson && (
                <div className="downloadFile-chart">
                    <MMEditor
                        onRef={(ref) => {
                            editor = ref.editor;
                        }}
                        dependence={false}
                        ifHideDialog={true}
                        appCode={record?.appCode}
                        orgCode={record?.orgCode}
                        graphData={graphJson}
                        type="chart"
                    />
                </div>
            )}
            {data?.map((item, index) => {
                return (
                    <DecisionTree
                        content={JSON.parse(item.content)}
                        key={index}
                        index={index}
                        uuid={record.uuid}
                        className={item.decisionToolCode}
                    />
                );
            })}
        </div>
    );
};
export default AssetsExport;
