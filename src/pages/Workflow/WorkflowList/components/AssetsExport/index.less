.assets-export {
    height: 0px !important;
    overflow: hidden;
    .graph-container {
        min-height: calc(100vh - 210px) !important;
        background-color: white;
        height: 500px !important;
    }
    .downloadFile-chart {
        // height: 0px !important;
        // display: none;
        // z-index: -102;
        position: relative;
        // width: 100% !important;

        .flow-detail-panel {
            min-height: calc(100vh - 210px);
        }
        .ant-card-bordered {
            border: none !important;
        }

        .flow-editor-bar {
            height: auto !important;
        }
        .publishType-title {
            color: black !important;
            font-weight: 500;
        }
        .first-tag {
            height: 20px;
            background: rgba(18, 187, 251, 0.1);
            border: 1px solid #12bbfb;
            border-radius: 2px;
            color: #12bbfb;
            margin-left: 27px !important;
        }
        .second-tag {
            height: 20px;
            background: rgba(130, 106, 249, 0.1);
            border: 1px solid #826af9;
            border-radius: 2px;
            color: #826af9;
            margin-left: 27px !important;
        }
        .publish-tag {
            background: rgba(7, 199, 144, 0.1);
            border: 1px solid #07c790;
            border-radius: 2px;
            color: #07c790;
            display: inline;
            padding: 2px 8px;
            margin-left: 27px !important;
        }
        .normal-tag {
            height: 20px;
            background: rgba(85, 169, 169, 0.1);
            border: 1px solid #55a9a9;
            border-radius: 2px;
            color: #55a9a9;
            margin-left: 17px;
        }
        .audit-detail-item {
            .cols {
                margin: 20px;
                width: 100%;
                height: 42px;
            }
            .cols {
                border: 0px !important;
            }
            .item {
                border-right: 1px solid #e1e6ee !important;
                border-left: 1px solid #e1e6ee !important;
            }
        }
        .rule-attr,
        .rule-condition {
            h4 {
                font-family: PingFangSC-Medium;
                font-size: 14px;
                color: #17233d;
                letter-spacing: 0;
                line-height: 20px;
                font-weight: 500;
                display: flex;
                align-items: center;
                &::before {
                    width: 6px;
                    height: 14px;
                    background: #17233d;
                    border-radius: 1px;
                }
            }
        }
        .rule-manage {
            min-width: auto !important;
            overflow-x: auto !important;
        }
        .ant-timeline-item-head-custom {
            top: 16.5px !important;
        }
        .audit-detail-timeline {
            padding: 30px 30px 0px 30px;
            background: #ffffff;
            border: 1px solid #e1e6ee;
        }
        .bread-container {
            display: inline-block;
            font-size: 14px;
            color: #444;
            line-height: 40px;
        }

        .audit-detail-body-main {
            position: relative;
            height: auto;
            background: #fff;

            .audit-detail-item {
                display: flex;
                border: 1px solid #e1e6ee;
                .left-config {
                    position: relative;
                    background: rgba(243, 245, 249, 0.5);
                    border-right: 1px solid #e1e6ee;
                    // height: 724px;
                    height: auto !important;
                    width: 260px;
                    ul {
                        margin: 0;
                        padding: 0;

                        li {
                            list-style: none;
                            border-bottom: 1px solid #ddd;
                            padding: 8px 12px;
                            cursor: pointer;
                            color: #03a9f4;
                        }

                        .li:hover {
                            background: #fff;
                        }

                        .active {
                            background: #fff;
                        }
                    }
                }

                .right-chart {
                    // height: 724px;
                    height: auto !important;
                    flex: 1;
                    .workflow-chart-content {
                        height: auto;
                    }
                }
            }

            .ant-tabs-bar {
                margin-bottom: 0;
            }

            .mt20 {
                margin-top: 20px;
            }
        }

        .shunt-tag {
            margin-left: 10px;
            background: rgba(18, 187, 251, 0.1);
            border: 1px solid #12bbfb;
            border-radius: 2px;
            color: #12bbfb;
            display: inline;
            padding: 2px 8px;
        }
        .bypass-tag {
            margin-left: 10px;
            width: 40px;
            height: 20px;
            background: rgba(130, 106, 249, 0.1);
            border: 1px solid #826af9;
            border-radius: 2px;
            color: #826af9 !important;
            display: inline;
            padding: 2px 8px;
        }
        .job-editor {
            height: 100%;
        }
        .mm-minimap {
            z-index: -101;
        }
        // z-index: -100;
        position: relative;
        // height: 300px;
        .return-link {
            position: absolute;
            color: #fff;
            padding: 2px 10px;
            background-color: rgba(0, 0, 0, 0.7);
            left: 0;
            top: 0;
            z-index: 1;
            font-size: 12px;
            cursor: pointer;
        }
    }
    .flow-editor {
        height: 500px;
    }
    .graph-container {
        height: 500px;
    }
}
