/*
 * @CreatDate: 2020-12-16 10:46:06
 * @Describe: 模板列表
 */

import I18N from '@/utils/I18N';
import { useState, useEffect } from 'react';
import { connect } from 'dva';
import { message, Button, Popconfirm, Switch, Handle, QueryListScene } from 'tntd';
import TablePage from '@/components/TablePage';
import { getUrlKey } from '@/utils/utils';
import { queryRouterHandle } from '@/utils/queryRouterHandle';
import QueryListWrapper from '@/components/QueryListWrapper';
import service from './service';
import LookModify from './modal/LookModify';
import AddModal from './modal/Add';
const { createActions } = QueryListScene;
const actions = createActions();
const Page = (props) => {
    const { dispatch, globalStore } = props;
    const { orgMap = {}, appMap = {}, currentApp } = globalStore;
    const name = getUrlKey('name') || '';
    const query = ({ current = 1, pageSize = 10, ...rest }) => {
        let params = {
            curPage: current,
            pageSize,
            appCode: currentApp.name,
            ...rest
        };

        queryRouterHandle.handle();

        return service.getList(params).then((res) => {
            if (!res) return;
            if (res.success) {
                let data = res?.data;
                return {
                    pageSize: Number(data?.pageSize),
                    current: Number(data?.curPage),
                    total: Number(data?.total),
                    data: data?.contents || []
                };
            }
        });
    };

    const [visible, setVisible] = useState(false);
    const [addVisible, setAddVisible] = useState(false);
    const [info, setInfo] = useState({
        appName: '',
        type: '',
        id: '',
        name: '',
        displayAppName: ''
    });

    useEffect(() => {
        if (name) {
            actions.setFormData({
                name
            });
        }
        return () => {
            queryRouterHandle.init();
        };
    }, [name]);

    const lookModify = async (record, type) => {
        setVisible(true);
        setInfo({
            ...record,
            type
        });
    };

    // 删除
    const deleteHandle = (id) => {
        service.deleteTemplate({ id }).then((res) => {
            if (res?.success) {
                message.success(res?.message);
                actions?.search();
            }
        });
    };

    const changeStatus = async (record, templateStatus) => {
        const result = record.graphJson && record.graphJson !== '{}';
        if (!result) return message.error(I18N.workflowtemplate.index.qingXianPeiZhiMo);
        const params = {
            id: record.id,
            templateStatus: templateStatus ? 1 : 0
        };
        service.enableSwitch(params).then((res) => {
            if (res?.success) {
                actions?.search();
            }
        });
    };

    const queryForm = [
        {
            type: 'input',
            name: 'name',
            props: {
                placeholder: I18N.workflowtemplate.index.qingShuRuMuBan
            }
        }
    ];

    const columns = [
        {
            title: I18N.workflowtemplate.index.muBanMingCheng,
            dataIndex: 'templateName'
        },
        {
            title: I18N.workflowtemplate.index.suoShuJiGou,
            dataIndex: 'orgCode',
            render: (value) => {
                return orgMap[value].name;
            }
        },
        {
            title: I18N.workflowtemplate.index.suoShuQuDao,
            dataIndex: 'appCode',
            render: (value) => {
                return appMap[value];
            }
        },
        {
            title: I18N.workflowtemplate.index.zhuangTai,
            dataIndex: 'templateStatus',
            width: 100,
            deleteCol: !window.auth('workflowTemplateManage', 'enable'),
            render: (text, record) => {
                return (
                    <Switch
                        checkedChildren={I18N.workflowtemplate.index.qiYong}
                        unCheckedChildren={I18N.workflowtemplate.index.jinYong}
                        checked={!!text}
                        onChange={(checked) => {
                            changeStatus(record, checked);
                        }}
                    />
                );
            }
        },
        {
            title: I18N.workflowtemplate.index.chuangJianRen,
            dataIndex: 'creator'
        },
        {
            title: I18N.workflowtemplate.index.chuangJianShiJian,
            dataIndex: 'gmtCreate',
            width: 190
        },
        {
            title: I18N.workflowtemplate.index.xiuGaiRen,
            dataIndex: 'operator'
        },
        {
            title: I18N.workflowtemplate.index.xiuGaiShiJian,
            dataIndex: 'gmtModify',
            width: 190
        },
        {
            title: I18N.workflowtemplate.index.caoZuo,
            dataIndex: 'operate',
            width: 150,
            operate: true,
            fixed: 'right',
            render: (text, record) => (
                <>
                    <a onClick={() => lookModify(record, 'chart')}>{I18N.workflowtemplate.index.chaKan}</a>
                    <a
                        onClick={() => {
                            if (!window.auth('workflowTemplateManage', 'update')) {
                                return false;
                            }
                            lookModify(record, 'edit');
                        }}
                        className={window.auth('workflowTemplateManage', 'update') ? '' : 'not-allow'}>
                        {I18N.workflowtemplate.index.xiuGai}
                    </a>
                    {window.auth('workflowTemplateManage', 'delete' && record?.templateStatus === 1) ? (
                        <Popconfirm
                            placement="topRight"
                            title={I18N.workflowtemplate.index.queDingShanChuCi}
                            onClick={(e) => e.stopPropagation()}
                            trigger="hover"
                            overlayStyle={{ width: 180 }}
                            onConfirm={() => deleteHandle(record.id)}>
                            <a>{I18N.workflowtemplate.index.shanChu}</a>
                        </Popconfirm>
                    ) : (
                        <a className="not-allow">{I18N.workflowtemplate.index.shanChu}</a>
                    )}
                </>
            )
        }
    ];

    const extralActions = [
        {
            hasPermission: window.auth('workflowTemplateManage', 'create'),
            render: (
                <Button onClick={() => setAddVisible(true)} type="primary">
                    {I18N.workflowtemplate.index.xinZeng}
                </Button>
            )
        }
    ];

    return (
        <>
            <TablePage
                actionKey="workflowTemplate"
                title={I18N.workflowtemplate.index.muBanLieBiao}
                query={query}
                actions={actions}
                initSearch={name}
                queryForm={queryForm}
                extralActions={extralActions}
                queryList={{
                    rowKey: 'id',
                    columns,
                    scroll: { x: 1300 }
                }}
                hasPermission={window.auth('workflowTemplateManage', 'query')}
            />
            <LookModify
                dispatch={dispatch}
                globalStore={globalStore}
                visible={visible}
                info={info}
                onOk={() => actions?.search()}
                onCancel={() => {
                    setVisible(false);
                    setInfo({});
                }}
            />
            <AddModal
                type="add"
                visible={addVisible}
                onOk={() => {
                    actions?.search({ current: 1 });
                }}
                onCancel={() => {
                    setAddVisible(false);
                }}
            />
        </>
    );
};

export default connect((state) => ({
    globalStore: state.global,
    productStore: state.product
}))(QueryListWrapper(Page, actions));
