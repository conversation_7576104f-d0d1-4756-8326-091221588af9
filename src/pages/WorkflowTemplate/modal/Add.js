/*
 * @CreatDate: 2020-07-29 19:56:12
 * @Describe: 创建
 */

import I18N from '@/utils/I18N';
import '../index.less';
import { useEffect, useState } from 'react';
import { connect } from 'dva';
import { Input, Select, Form, message, TreeSelect, Modal } from 'tntd';
import { traverseTree } from '@/utils/utils';
import { userAPI } from '@/services';
import service from '../service';

const { Option } = Select;

const AddModal = (props) => {
    const { visible, globalStore, type, content = '', from, bpmnStr, appCode = '', orgCode } = props;
    const { appList = [], currentOrgCode } = globalStore;
    const { getFieldDecorator, setFieldsValue } = props.form;
    const { orgList } = globalStore;
    const [loading, setLoading] = useState(false);
    const [appLists, setAppLists] = useState();
    // useEffect(() => {
    //     getAppList(currentOrgCode);
    // }, []);
    const handleOk = () => {
        props.form.validateFields((err, values) => {
            if (err) return;
            setLoading(true);
            service
                .createTemplate({
                    ...values,
                    graphJson: props.data ? JSON.stringify(props.data) : undefined
                })
                .then((res) => {
                    if (res && res.success) {
                        message.success(res.message);
                        props.onOk();
                        props.onCancel();
                        props.form.resetFields();
                    }
                })
                .finally(() => {
                    setLoading(false);
                });
        });
    };
    const getAppList = (orgCode) => {
        let findItem;
        traverseTree(orgList, (node) => {
            if (node.code === orgCode) {
                findItem = node;
                return false;
            }
        });
        userAPI.getAppByOrgId({ orgUuid: findItem.uuid }).then((res) => {
            setAppLists(res?.data);
        });
    };
    return (
        <Modal
            title={I18N.modal.add.xinZengMuBan}
            visible={visible}
            maskClosable={false}
            className="workflow-template-add"
            confirmLoading={loading}
            onOk={handleOk}
            onCancel={() => {
                props.onCancel();
                props.form.resetFields();
            }}>
            <Form labelCol={{ span: 6 }} wrapperCol={{ span: 14 }}>
                <Form.Item label={I18N.modal.add.suoShuJiGou}>
                    {getFieldDecorator('orgCode', {
                        initialValue: type !== 'add' ? orgCode : undefined,

                        rules: [
                            {
                                required: true,
                                message: I18N.modal.add.qingTianXieJiGou
                            }
                        ]
                    })(
                        <TreeSelect
                            disabled={type !== 'add'}
                            placeholder={I18N.modal.add.xuanZeJiGou}
                            searchPlaceholder={I18N.modal.add.jiGouMingCheng}
                            treeNodeFilterProp="title"
                            showSearch
                            treeData={orgList}
                            treeDefaultExpandAll
                            style={{ width: '100%', height: 28, lineHeight: 28 }}
                            dropdownStyle={{ maxHeight: 300, overflow: 'auto', width: 340 }}
                            onChange={(value) => {
                                setFieldsValue({ appCode: undefined });
                                getAppList(value);
                            }}
                        />
                    )}
                </Form.Item>
                <Form.Item label={I18N.modal.add.suoShuQuDao}>
                    {getFieldDecorator('appCode', {
                        initialValue: type !== 'add' ? appCode : undefined,
                        rules: [
                            {
                                required: true,
                                message: I18N.modal.add.qingTianXieQuDao
                            }
                        ]
                    })(
                        <Select
                            disabled={type !== 'add'}
                            placeholder={I18N.modal.add.qingXuanZeQuDao}
                            showSearch
                            optionFilterProp="children"
                            dropdownMatchSelectWidth={false}>
                            {appLists &&
                                appLists
                                    .filter((item) => item.have)
                                    .map((item, index) => {
                                        return <Option value={item.name}>{item.displayName}</Option>;
                                    })}
                        </Select>
                    )}
                </Form.Item>
                <Form.Item label={I18N.modal.add.muBanMingCheng}>
                    {getFieldDecorator('templateName', {
                        rules: [
                            {
                                required: true,
                                message: I18N.modal.add.qingTianXieCeLue
                            },
                            { pattern: /^[a-zA-Z0-9_\u4e00-\u9fa5]+$/, message: I18N.modal.add.mingChengBiXuYou }
                        ]
                    })(<Input placeholder={I18N.modal.add.jinZhiChiZhongYing} />)}
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default Form.create()(
    connect((state) => ({
        globalStore: state.global
    }))(AddModal)
);
