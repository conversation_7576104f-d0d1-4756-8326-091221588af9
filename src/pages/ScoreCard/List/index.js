/*
 * @CreatDate: 2021-04-14 15:23:31
 * @Describe: 决策工具Tab
 */

import I18N from '@/utils/I18N';
import { useState } from 'react';
import { connect } from 'dva';
import { routerRedux } from 'dva/router';
import { Tabs } from 'tntd';
import { getUrlKey } from '@tntd/utils';
import EditorArea from './EditorArea';
import RunningArea from './RunningArea';
const TabPane = Tabs.TabPane;

const List = (props) => {
    const { history, location, dispatch } = props;
    const currentTab = getUrlKey('currentTab') || '1';
    const [status] = useState(getUrlKey('status'));

    const [key, setKey] = useState('1');

    const changeTab = (key) => {
        const { pathname } = location;
        const search = '?currentTab=' + key;
        dispatch(routerRedux.push(pathname + search));
        setKey(key);
    };

    return (
        <div className="main-area-wrap">
            <div className="page-global-tab">
                <Tabs activeKey={currentTab || key} onChange={changeTab}>
                    <TabPane tab={I18N.list.index.yunXingQu} key="1">
                        <RunningArea history={history} status={status} currentTab={currentTab} />
                    </TabPane>
                    <TabPane tab={I18N.list.index.bianJiQu} key="2">
                        <EditorArea history={history} status={status} currentTab={currentTab} />
                    </TabPane>
                </Tabs>
            </div>
        </div>
    );
};

export default connect((state) => ({
    globalStore: state.global
}))(List);
