import I18N from '@/utils/I18N';
export const nameStyle = {
    1: {
        background: 'rgba(18, 107, 251, 0.1)',
        border: '1px solid rgb(18, 107, 251)',
        borderRadius: '2px',
        color: 'rgb(18, 107, 251)'
    },
    2: {
        background: 'rgba(18, 107, 251, 0.1)',
        border: '1px solid rgb(18, 107, 251)',
        borderRadius: '2px',
        color: 'rgb(18, 107, 251)'
    },
    3: {
        background: 'rgba(18, 107, 251, 0.1)',
        border: '1px solid rgb(18, 107, 251)',
        borderRadius: '2px',
        color: 'rgb(18, 107, 251)'
    },
    4: {
        background: 'rgba(7, 199, 144, 0.1)',
        border: '1px solid rgb(7, 199, 144)',
        borderRadius: '2px',
        color: 'rgb(7, 199, 144)'
    },
    5: {
        background: 'rgba(94, 112, 146, 0.1)',
        border: '1px solid rgb(94, 112, 146)',
        borderRadius: '2px',
        color: 'rgb(94, 112, 146)'
    }
};

export const auditStatusMap = {
    1: {
        value: I18N.list.contants.daiFaBu,
        color: '#126BFB'
    },
    2: {
        value: I18N.list.contants.daiShenHe,
        color: 'rgb(247, 176, 54)'
    },
    3: {
        value: I18N.list.contants.daiFuHe,
        color: '#ff9845'
    },
    4: {
        value: I18N.list.contants.yiFaBu,
        color: '#07C790'
    }
};
