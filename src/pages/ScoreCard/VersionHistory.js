import I18N from '@/utils/I18N';
import { useState, useEffect } from 'react';
import { Modal, Form, Table, Button, message, Tag, Breadcrumb, Spin, Ellipsis, Handle } from 'tntd';
import { connect } from 'dva';
import { formatStandardTime, getUrlKey } from '@/utils/utils';

import history from '@/utils/history';
import service from './service';
import { nameStyle } from './List/contants';
import otp from '@/pages/ScoreCard/otp';

const { confirm } = Modal;

const VersionHistory = (props) => {
    const { match } = props;
    const { params } = match;
    const { scoreCardUuid } = params;
    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(false);
    const currentTab = getUrlKey('currentTab');
    useEffect(() => {
        getHistory();
        setLoading(true);

        return () => {
            setData([]);
        };
    }, []);

    const getHistory = () => {
        let params = {
            scoreCardUuid
        };
        service
            .historyList(params)
            .then((res) => {
                if (res?.success) {
                    setData(res?.data);
                }
            })
            .finally(() => {
                setLoading(false);
            });
    };
    // 查看q
    const viewHandle = (record, type) => {
        if (!loading) {
            history.push(
                `/noah/bodyguard/scoreCard/detail?scoreCardUuid=${record?.scoreCardUuid}&scoreCardVersionUuid=${record?.scoreCardVersionUuid}&pageType=view&version=${record?.version}&currentTab=${currentTab}`
            );
        }
    };

    const onCover = (record) => {
        confirm({
            title: I18N.scorecard.versionhistory.fuGaiBianJiQu,
            content: I18N.scorecard.versionhistory.ninQueDingYaoJiang,
            cancelText: I18N.scorecard.versionhistory.quXiao,
            okText: I18N.scorecard.versionhistory.queDing,
            onOk() {
                service.scorecardCover({ scoreCardVersionUuid: record?.scoreCardVersionUuid }).then(async (res) => {
                    if (res?.success) {
                        await getHistory();
                        message.success(res?.message);
                    }
                });
            }
        });
    };
    const columns = [
        {
            title: I18N.scorecard.versionhistory.pingFenKaMingCheng,
            width: 350,
            dataIndex: 'scoreCardName',
            render: (text, record) => {
                let style = nameStyle;
                return (
                    <>
                        <Tag style={style[record.versionStatus]}>{record?.versionStatusName}</Tag>
                        <Tag color="rgba(148, 95, 185, 0.1)" style={{ border: '1px solid #945FB9', color: '#945FB9' }}>
                            V{record?.version}
                        </Tag>
                        <div style={{ display: 'inline-block' }}>
                            <Ellipsis widthLimit={180} title={text} />
                        </div>
                    </>
                );
            }
        },
        {
            title: I18N.scorecard.versionhistory.biaoZhi,
            dataIndex: 'scoreCardCode',
            width: '220px',
            render: (text) => {
                return <Ellipsis title={text} />;
            }
        },

        {
            dataIndex: 'publishDesc',
            title: I18N.scorecard.versionhistory.faBuMiaoShu,
            width: 180,
            render: (text) => {
                return <span style={{ display: 'inline-block' }}>{text ? <Ellipsis widthLimit={150} title={text} /> : '--'}</span>;
            }
        },
        {
            title: I18N.scorecard.versionhistory.xiuGaiRen,
            dataIndex: 'updateBy',
            width: 150,
            render: (text) => {
                return <Ellipsis title={text} />;
            }
        },
        {
            title: I18N.scorecard.versionhistory.xiuGaiShiJian,
            dataIndex: 'gmtModify',
            width: 190,
            render: (text, record) => {
                return formatStandardTime(record?.gmtModify);
            }
        },
        {
            title: I18N.scorecard.versionhistory.chuangJianRen,
            dataIndex: 'createBy',
            width: 150,
            render: (text) => {
                return <Ellipsis title={text} />;
            }
        },
        {
            title: I18N.scorecard.versionhistory.chuangJianShiJian,
            dataIndex: 'gmtCreate',
            width: 190,
            render: (text, record) => {
                return formatStandardTime(record?.gmtCreate);
            }
        },
        {
            dataIndex: 'operation',
            title: I18N.scorecard.versionhistory.caoZuo,
            width: otp.actionWidth,
            render: (text, record) => {
                return (
                    <Handle>
                        <a
                            onClick={() => {
                                viewHandle(record, 'look');
                            }}>
                            {I18N.scorecard.versionhistory.chaKan}
                        </a>

                        <a
                            onClick={() => {
                                onCover(record);
                            }}>
                            {I18N.scorecard.versionhistory.fuGaiBianJiQu}
                        </a>
                    </Handle>
                );
            }
        }
    ];
    return (
        <>
            <div className="g-formula-history">
                {/* <div className="page-global-header">
                    <Breadcrumb separator=">">
                        <Breadcrumb.Item
                            onClick={() => {
                                history.push('/noah/bodyguard/scoreCard?currentTab=' + currentTab);
                            }}>
                            <a>评分卡</a>
                        </Breadcrumb.Item>
                        <Breadcrumb.Item>版本历史</Breadcrumb.Item>
                    </Breadcrumb>
                </div> */}
                <div className="page-global-body">
                    <Spin spinning={loading}>
                        <Table dataSource={data} columns={columns} scroll={{ x: 950 }} pagination={{ hideOnSinglePage: true }} />
                    </Spin>
                </div>
            </div>
        </>
    );
};

export default connect((state) => ({
    globalStore: state.global
}))(VersionHistory);
