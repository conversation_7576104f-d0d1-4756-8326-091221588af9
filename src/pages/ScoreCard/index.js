import I18N from '@/utils/I18N';
import { Route, Switch } from 'dva/router';
import BreadCrumb from '@tddc/bread-crumb';
import Detail from './Detail';
import List from './List';
import VersionHistory from './VersionHistory';

const Entrance = () => {
    return (
        <Switch>
            <Route
                name={I18N.scorecard.index.banBenLiShi}
                component={VersionHistory}
                path="/noah/bodyguard/scoreCard/versionHistory/:scoreCardUuid/:scoreCardVersionUuid"
            />
            <Route exact name={I18N.scorecard.index.pingFenKaPeiZhi} component={Detail} path="/noah/bodyguard/scoreCard/detail" />
            <Route component={List} path="/" />
            <Route component={Detail} path="/Detail" />
        </Switch>
    );
};

export default BreadCrumb(Entrance);
