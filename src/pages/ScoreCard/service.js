import { getUrl, deleteEmptyObjItem, getHeader } from '@/services/common';
import request, { downloadFileHandle } from '@/utils/request';

//列表
const getList = async (params) => {
    return request(getUrl(`/scorecard/page/${params.type}`, params), {
        method: 'GET'
    });
};
//授权
const Authorize = (params) => {
    return request('/scorecard/authorize', {
        method: 'POST',
        headers: {
            ...getHeader(),
            'Content-Type': 'application/json'
        },
        body: { ...params }
    });
};
//批量发布上线
const onLine = async (params) => {
    return request(getUrl('/scorecard/onLine'), {
        method: 'POST',
        body: params,
        headers: {
            ...getHeader(),
            'Content-Type': 'application/json'
        }
    });
};
//批量发布上线
const batchOnLine = async (params) => {
    return request(getUrl('/scorecard/batchOnLine'), {
        method: 'POST',
        body: params,
        headers: {
            ...getHeader(),
            'Content-Type': 'application/json'
        }
    });
};
//校验评分卡标识
const checkCode = async (params) => {
    return request(getUrl('/scorecard/checkCode', params), {
        method: 'GET'
    });
};
//校验评分卡名称
const checkName = async (params) => {
    return request(getUrl('/scorecard/checkName', params), {
        method: 'GET'
    });
};
//复制
const copy = async (params) => {
    return request('/scorecard/copy', {
        method: 'POST',
        body: params,
        headers: {
            ...getHeader(),
            'Content-Type': 'application/json'
        }
    });
};
//版本覆盖
const scorecardCover = async (params) => {
    return request(getUrl('/scorecard/cover'), {
        method: 'POST',
        body: params,
        headers: {
            ...getHeader(),
            'Content-Type': 'application/json'
        }
    });
};
// 创建评分卡
const scorecardCreate = async (params) => {
    return request('/scorecard/create', {
        method: 'POST',
        body: params,
        headers: {
            ...getHeader(),
            'Content-Type': 'application/json'
        }
    });
};
// 删除评分卡
const scorecardDelete = async (params) => {
    return request(getUrl('/scorecard/delete'), {
        method: 'POST',
        body: params,
        headers: {
            ...getHeader(),
            'Content-Type': 'application/json'
        }
    });
};

// 历史版本
const historyList = async (params) => {
    return request(getUrl('/scorecard/historyList', params), {
        method: 'GET'
    });
};
// 下线
const offline = async (params) => {
    return request(getUrl('/scorecard/offLine'), {
        method: 'POST',
        body: { ...params },
        headers: {
            ...getHeader(),
            'Content-Type': 'application/json'
        }
    });
};

// 获取评分卡入参
const scorecardSelectList = async (params) => {
    return request(getUrl('/scorecard/select', params), {
        method: 'GET'
    });
};

// 出参设置
const setOutPutParams = async (params) => {
    return request(getUrl('/scorecard/setOutPutParams'), {
        method: 'POST',
        body: { ...params },
        headers: {
            ...getHeader(),
            'Content-Type': 'application/json'
        }
    });
};
// 获取评分卡测试
const test = async (params) => {
    return request(getUrl('/scorecard/test'), {
        method: 'POST',
        body: { ...params },
        headers: {
            ...getHeader(),
            'Content-Type': 'application/json'
        }
    });
};
// 获取评分卡入参
const getInput = async (params) => {
    return request(getUrl('/scorecard/testFields', params), {
        method: 'GET'
    });
};
// 更新评分卡
const update = async (params) => {
    return request(getUrl('/scorecard/update'), {
        method: 'POST',
        body: params,
        headers: {
            ...getHeader(),
            'Content-Type': 'application/json'
        }
    });
};

// 获取评分卡出参列表
const outPutItemList = async (params) => {
    return request(getUrl('/scorecard/outPutItemList', params), {
        method: 'POST',
        body: { ...params },
        headers: {
            ...getHeader(),
            'Content-Type': 'application/json'
        }
    });
};

// 运行趋势导出Excel
const exportExcel = async (params, name, fileType) => {
    return downloadFileHandle(
        getUrl('/scorecard/exportExcel', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        name,
        fileType
    );
};

/**
 * 获取评分卡详情
 * @param {*} type run ｜ edit
 * @param {*} scoreCardUuid
 * @param {*} scoreCardVersionUuid
 * @returns
 */
const apiGetScarecardDetail = async ({ type, scoreCardUuid, scoreCardVersionUuid }) => {
    return request(getUrl(`/scorecard/getDetail/${type}`, { scoreCardUuid, scoreCardVersionUuid }), {
        method: 'GET'
    });
};

/** 校验评分卡第一步 */
const apiCheckNameCode = async ({ scoreCardCode, scoreCardName, scoreCardUuid }) => {
    return request(getUrl('/scorecard/checkNameCode', { scoreCardCode, scoreCardName, scoreCardUuid }), {
        method: 'GET'
    });
};

/** 校验评分卡第二步 */
const apiCheckContent = async ({ content, scoreCardMode }) => {
    return request(getUrl('/scorecard/contentCheck'), {
        method: 'POST',
        body: { content, scoreCardMode },
        headers: {
            ...getHeader(),
            'Content-Type': 'application/json'
        }
    });
};
// 获取当前评分卡详情
const scorecardDetail = async (params) => {
    return request(getUrl('/scorecard/queryList', params), {
        method: 'GET'
    });
};
export default {
    scorecardDetail,
    exportExcel,
    outPutItemList,
    getList,
    Authorize,
    batchOnLine,
    checkCode,
    checkName,
    copy,
    scorecardCover,
    scorecardCreate,
    scorecardDelete,
    historyList,
    offline,
    scorecardSelectList,
    test,
    getInput,
    update,
    onLine,
    setOutPutParams,
    apiGetScarecardDetail,
    apiCheckNameCode,
    apiCheckContent
};
