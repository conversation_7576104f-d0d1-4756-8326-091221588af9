import I18N, { getLang } from '@/utils/I18N';
import { Row, Col, Tooltip } from 'tntd';
import GGEditor, { Flow, RegisterNode, Command, ContextMenu, NodeMenu, EdgeMenu, MultiMenu, CanvasMenu, RegisterCommand } from 'gg-editor';
import { transformRuleAndIndexFieldList } from '@/utils/decision';
import { useEffect, useRef, useState, useMemo } from 'react';
import { connect } from 'dva';
import { isJSON } from '@tntd/utils';
import FlowToolbar from '@/components/Flow/FlowToolbar.js';
import FlowDetailPanel from '@/components/Flow/FlowDetailPanel';
import service from '../../service';
const DecisionTree = (props) => {
    let { decisionTreeStore, globalStore, isAdd, baseData, auditStore, versions, dispatch, content, type } = props;
    let { dialogData } = decisionTreeStore;
    // let detailRef.current = {};
    const detailRef = useRef({ this: {} });

    const ruleAndIndexFieldList = useMemo(() => {
        if (baseData?.appCode && baseData?.orgCode) {
            return transformRuleAndIndexFieldList(globalStore.allMap, baseData?.appCode, baseData?.orgCode);
        }
        return [];
    }, [baseData, globalStore.allMap]);

    const [toolTipInfo, setToolTipInfo] = useState({
        visible: false,
        top: 0,
        left: 0
    });

    useEffect(() => {
        async function apiGetDealTypes() {
            const dealTypes = await service.getDealTypes();
            dispatch({
                type: 'decisionTree/setAttrValue',
                payload: {
                    dealTypeList: dealTypes.data || []
                }
            });
        }
        apiGetDealTypes();
    }, []);
    // const this = {};

    // useEffect(() => {
    //     let dom = document.getElementsByClassName('graph-container')[0];
    //     dom.onkeydown = (event) => {
    //         if (event) {
    //             //alert("不允许使用任何键盘按键");
    //             return false;
    //         }
    //     };
    // }, []);
    //决策树
    useEffect(() => {
        if (detailRef.current?.page) {
            // 只读模式
            dispatch({
                type: 'decisionTree/setAttrValue',
                payload: {
                    operatorType: 'look'
                }
            });
            // 联调 缺少通过uuid查询决策工具详情接口

            content.nodes &&
                content.nodes.forEach((k) => {
                    k.allLabel = k.allLabel || k.label;
                    let filterLabel = k.allLabel;
                    if (filterLabel.length > 10) {
                        filterLabel = filterLabel.substring(0, 10) + '...';
                    }
                    k.label = filterLabel;
                    k.size = '140*32';
                });
            const fieldListAll = ruleAndIndexFieldList;
            content.nodes.forEach((item) => {
                if (item.shape === 'flow-capsule' && item.fieldDetail && item.fieldDetail.dataType === 'enum') {
                    const fieldDetail = item.fieldDetail;
                    const findobj = fieldListAll.find((item) => item.name === fieldDetail.name);
                    if (findobj) {
                        item.fieldDetail.fieldInfo = findobj.fieldInfo;
                    }
                }
                if (item.shape === 'flow-capsule' && item.fieldDetail) {
                    const fieldDetail = item.fieldDetail;
                    const findObj = fieldListAll.find((k) => k.name === fieldDetail.name);
                    if (!findObj) {
                        item.label = I18N.flow.index.jieDian;
                        item.fieldDetail = null;
                    } else {
                        item.label = findObj.dName?.length > 10 ? findObj?.dName?.slice(0, 10) + '...' : findObj.dName;
                        item.allLabel = findObj.dName;
                        // if (getLang() === 'cn') {
                        //     item.label = findObj.dName;
                        //     item.allLabel = findObj.dName;
                        // } else {
                        //     item.label = findObj.name;
                        //     item.allLabel = findObj.name;
                        // }
                    }
                }
            });
            detailRef.current.page.read(content); // 初始化数据
        }
    }, [detailRef.current?.page]);

    const renderFlow = () => {
        let align = {
            item: true,
            grid: true
        };
        return (
            <Flow
                onClick={(e) => {
                    return false;
                }}
                onBeforeChange={(e) => {
                    if (e.action === 'remove') {
                        return false;
                    }
                }}
                className="flow"
                ref={(g) => (detailRef.current.page = g ? g.page : null)}
                align={align}
                item={true}
                noEndEdge={false} // 是否支持悬空边
                // onDragStart={(e) => { }}
                onAnchorDrop={(e) => {
                    if (e.type !== 'node') {
                        const beforeNodeId = e?.currentShape?.getItem()?.id;
                        const targetNodeId = e?.shape?.getItem()?.id;

                        if (beforeNodeId === targetNodeId) {
                            return null;
                        }
                        e?.currentShape?.eventPreFix?.includes('edge') &&
                            detailRef.current.update(e?.currentShape?.getItem(), { label: '', conditionsGroup: { conditions: [] } });
                    }
                }}
                onNodeMouseEnter={(e) => {
                    if (e?.item?.model?.allLabel?.length > 10) {
                        const { top, left } = document.querySelector('.flow').getBoundingClientRect() || {};
                        const { x, y } = e.item.graph.getDomPoint({
                            x: e.item.model.x,
                            y: e.item.model.y
                        });
                        let obj = {
                            visible: true,
                            top: y + top - e.item.bbox.height / 2,
                            left: x + left,
                            text: e.item.model.allLabel
                        };
                        setToolTipInfo(obj);
                    }
                }}
                onNodeMouseLeave={(e) => {
                    if (e?.item?.model?.allLabel?.length > 10) {
                        let obj = {
                            toolTipInfo: {
                                visible: false,
                                top: 0,
                                left: 0,
                                text: null
                            }
                        };
                        setToolTipInfo(obj);
                    }
                }}
                onEdgeMouseEnter={(e) => {
                    if (e?.item?.model?.allLabel?.length > 10) {
                        const { top, left } = document.querySelector('.flow').getBoundingClientRect() || {};
                        const { x, y } = e.item.graph.getDomPoint({
                            x: e.shape.getBBox?.()?.x || e?.x,
                            y: e.shape.getBBox?.()?.y || e?.y
                        });
                        let obj = {
                            visible: true,
                            top: y + top, // + e.shape.getBBox().height / 2,
                            left: x + left + e.shape.getBBox().width / 2,
                            text: e.item.model.allLabel
                        };
                        setToolTipInfo(obj);
                    }
                }}
                onEdgeMouseLeave={(e) => {
                    if (e?.item?.model?.allLabel?.length > 10) {
                        let obj = {
                            toolTipInfo: {
                                visible: false,
                                top: 0,
                                left: 0,
                                text: null
                            }
                        };
                        setToolTipInfo(obj);
                    }
                }}
            />
        );
    };

    return (
        <>
            <GGEditor
                onBeforeCommandExecute={({ command }) => {
                    // 只读模式下不支持删除／新增／更新
                    if (['add', 'delete', 'update'].includes(command.name)) {
                        // 阻止命令执行，避免无限循环
                        return false;
                    }
                }}
                className="flow-editor"
                ref={(g) => (detailRef.current.editor = g ? g.editor : null)}>
                <Row type="flex" className="flow-editor-bd">
                    {/* <Col span={3} className="flow-editor-bar">
                                    <FlowItemPanel />
                                </Col> */}
                    <Col span={18} className="flow-editor-content">
                        <Row type="flex" className="flow-editor-hd">
                            <Col span={24}>
                                <FlowToolbar mode="view" />
                            </Col>
                        </Row>
                        {renderFlow()}
                    </Col>
                    <Col span={6} className="flow-editor-bar">
                        <FlowDetailPanel
                            onRef={(ref) => {
                                detailRef.current.update = ref;
                            }}
                        />
                        {/* <FlowMinimap/>*/}
                    </Col>
                </Row>

                <RegisterNode
                    name="flow-policy"
                    extend="flow-capsule"
                    config={{
                        anchor: [[0.5, 0]]
                    }}
                />
                {/* 节点右键菜单 */}
                <ContextMenu className="flow-editor-menu-wrap">
                    {/* <NodeMenu>
                        <Command name="toFront" disabled={true}>
                            向上一层
                        </Command>
                        <Command name="toBack">向下一层</Command>
                    </NodeMenu> */}
                    {/* <EdgeMenu>
                        <Command name="delete">删除</Command>
                    </EdgeMenu> */}
                    <CanvasMenu>
                        <Command name="selectAll">{I18N.components.modeltool.quanXuan}</Command>
                        <hr />
                        <Command name="resetZoom">{I18N.components.modeltool.shiJiChiCun}</Command>
                        <Command name="autoZoom">{I18N.components.modeltool.ziShiYingChiCun}</Command>
                    </CanvasMenu>
                </ContextMenu>
            </GGEditor>
            {toolTipInfo.visible && (
                <Tooltip title={toolTipInfo.text} visible={true}>
                    <div
                        style={{
                            position: 'fixed',
                            top: toolTipInfo.top,
                            left: toolTipInfo.left
                        }}
                    />
                </Tooltip>
            )}
        </>
    );
};
export default connect((state) => ({
    globalStore: state.global,
    decisionTreeStore: state.decisionTree
}))(DecisionTree);
