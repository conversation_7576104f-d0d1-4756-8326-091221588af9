/*
 * @Descripttion:
 * @Author: jun.zhang
 * @Date: 2021-02-24 12:31:06
 * @LastEditTime: 2022-04-27 09:28:23
 */
const fs = require('fs');
const path = require('path');
const assetsPublicPath = '/';
const PORT = 7201;
const MOCK = true;
const sourcePrefix = 'noah-resource/';
const publicPath = '/noah-resource/';
const Properties = require('./plugins/props-loader/Properties');
const prop = new Properties();
try {
    const str = fs.readFileSync(path.join(__dirname, '..', '.local'), 'utf8');
    prop.load(str);
} catch (e) { }

// const proxyUrl = 'https://zhice.tcloud.tongdun.cn/';
// const proxyUrl = 'http://************:8000';
const proxyUrl = 'http://************:8088/';

module.exports = {
    projectCode: 'noah',
    common: {
        htmlTemplatePath: path.resolve(__dirname, '../src/index.ejs'),
        sourcePrefix
    },
    dev: {
        hot: true,
        mock: MOCK,
        port: PORT,
        assetsSubDirectory: sourcePrefix + '/static',
        assetsPublicPath: '/',
        proxyTable: {
            '/bridgeApi': {
                target: proxyUrl,
                changeOrigin: true,
                pathRewrite: {
                    // '^/bridgeApi': '/api'
                }
            },
            '/indexApi': {
                target: proxyUrl,
                changeOrigin: true,
                pathRewrite: {
                    // '^/dashboardApi': '/api'
                }
            },
            '/dashboardApi': {
                target: proxyUrl,
                changeOrigin: true,
                pathRewrite: {
                    // '^/dashboardApi': '/api'
                }
            },
            '/noahApi': {
                target: proxyUrl,
                changeOrigin: true,
                pathRewrite: {
                    // '^/noahApi': '/api'
                }
            },
            '/dataApi': {
                target: proxyUrl,
                changeOrigin: true,
                pathRewrite: {
                    // '^/noahApi': '/api'
                }
            },
            '/creditApi': {
                target: proxyUrl,
                changeOrigin: true,
                pathRewrite: {
                    // '^/noahApi': '/api'
                }
            },
            '/mockApi': {
                target: 'https://sinan.tongdun.me/mock/15705/', //
                changeOrigin: true,
                pathRewrite: {
                    '^/mockApi': '/api'
                }
            }
        },
        autoOpenBrowser: true,
        devtool: 'eval-source-map',
        publicPath,
        host: 'localhost',
        env: {
            MOCK,
            PORT,
            POC: false,
            SYS_ENV: 'development',
            BABEL_ENV: 'development'
        }
    },
    build: {
        assetsRoot: path.resolve(__dirname, '../dist'),
        assetsSubDirectory: sourcePrefix + '/static',
        assetsPublicPath,
        devtool: 'source-map',
        env: {
            PORT,
            POC: false,
            SYS_ENV: 'production',
            BABEL_ENV: 'production'
        }
    }
};
