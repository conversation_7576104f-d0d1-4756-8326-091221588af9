# 删除功能卡死问题修复验证

## 问题描述
当用户在Flow组件中点击删除按钮时，页面会卡死。

## 问题根因
在 `onBeforeCommandExecute` 事件处理中存在无限循环：
1. 用户点击删除 → 触发 `onBeforeCommandExecute`
2. 检测到删除命令 → 调用 `command.back()` 撤销
3. `command.back()` 又触发新的命令执行 → 再次触发 `onBeforeCommandExecute`
4. 形成无限循环，导致页面卡死

## 修复方案
将原来的异步撤销操作改为直接返回 `false` 阻止命令执行：

### 修复前：
```javascript
onBeforeCommandExecute={({ command }) => {
    if (!isEditor && !isAdd) {
        if (['add', 'delete', 'update'].includes(command.name)) {
            this.undoTimeout = setTimeout(() => {
                command.back(this.editor);
            }, 0);
        }
    }
}}
```

### 修复后：
```javascript
onBeforeCommandExecute={({ command }) => {
    if (!isEditor && !isAdd) {
        if (['add', 'delete', 'update'].includes(command.name)) {
            // 阻止命令执行，避免无限循环
            return false;
        }
    }
}}
```

## 修复的文件
1. `src/components/Flow/index.js` - 主要的Flow组件
2. `src/pages/Audit/Components/ModelTool/DecisionTree.js` - 审计页面的决策树组件

## 额外改进
1. 添加了 `componentWillUnmount` 生命周期方法来清理定时器，防止内存泄漏
2. 使用 `return false` 直接阻止命令执行，而不是先执行再撤销

## 测试验证
请按以下步骤验证修复：

1. 打开包含Flow组件的页面
2. 确保处于只读模式（`isEditor=false` 且 `isAdd=false`）
3. 右键点击节点，选择删除
4. 验证：
   - 页面不会卡死
   - 删除操作被正确阻止
   - 没有出现无限循环
   - 浏览器控制台没有错误信息

## 预期结果
- 在只读模式下，删除操作被阻止但不会导致页面卡死
- 在编辑模式下，删除操作正常工作
- 没有内存泄漏问题
